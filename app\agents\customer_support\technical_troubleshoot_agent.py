"""
Technical Troubleshooting Agent - LangGraph Workflow Implementation.

This agent uses Claude Code to perform deep codebase analysis, identify root causes,
detect workarounds, and automatically create bug tickets when confidence is high.

Workflow:
1. Extract Technical Details → Parse error codes, stack traces
2. Invoke Claude Code → Deep code analysis (with LLM fallback)
3. Check Workarounds → Search KB + similar tickets
4. Validate Bug → Confidence scoring + auto-ticket decision
5. Create Jira Ticket (Conditional) → Auto-create if confidence >= threshold
6. Format Output → Structured response
"""
import asyncio
import json
import re
from dataclasses import dataclass
from typing import Dict, Any, List, Optional, TypedDict
from langchain_core.messages import SystemMessage, HumanMessage
from langgraph.graph import StateGraph, END

from app.agents.customer_support.base_agent import BaseAgent, WorkflowContext, AgentStatus
from app.agents.registry import register_agent
from app.models.integrations.claude_code_client import ClaudeCodeClient, CodeAnalysisResult
from app.models.integrations.jira_client import JiraClient
from app.models.integrations.llm_factory import create_anthropic_llm
from config.settings import Config
from config.logging import get_logger

logger = get_logger(__name__)


# ============================================================================
# State Definition
# ============================================================================

class TroubleshootState(TypedDict):
    """State for troubleshooting workflow."""
    # Input
    ticket_input: Dict[str, Any]
    context: WorkflowContext

    # Processing
    technical_context: Dict[str, Any]
    code_analysis: Dict[str, Any]
    workarounds: List[Dict[str, Any]]
    bug_validation: Dict[str, Any]
    jira_ticket: Optional[Dict[str, Any]]

    # Output
    final_output: Optional[Dict[str, Any]]
    errors: List[str]
    metadata: Dict[str, Any]


# ============================================================================
# Workflow Nodes
# ============================================================================

class TroubleshootingWorkflowNodes:
    """Node implementations for troubleshooting workflow."""

    def __init__(self, llm, claude_code_client: ClaudeCodeClient, jira_client: JiraClient):
        self.llm = llm
        self.claude_code_client = claude_code_client
        self.jira_client = jira_client
        self.logger = get_logger(__name__)

    def extract_technical_details_node(self, state: TroubleshootState) -> TroubleshootState:
        """
        Extract technical information from support ticket.

        Extracts:
        - Error codes (HTTP status, error messages)
        - Stack traces
        - Affected components
        - Urgency indicators
        """
        self.logger.info("Extracting technical details from ticket")

        ticket_input = state["ticket_input"]
        title = ticket_input.get("title", "")
        description = ticket_input.get("description", "")
        full_text = f"{title}\n\n{description}"

        # Extract error codes (HTTP codes, error constants)
        error_codes = re.findall(r'\b(HTTP\s*)?([45]\d{2}|ERROR_\w+)\b', full_text, re.IGNORECASE)
        error_codes = list(set([code[1] for code in error_codes]))

        # Extract stack trace (look for file references)
        stack_trace_match = re.search(
            r'(Traceback|Stack trace|at .*?\(.*?:\d+\)).*?(\n\s+.*?)*',
            full_text,
            re.IGNORECASE | re.MULTILINE
        )
        stack_trace = stack_trace_match.group(0) if stack_trace_match else None

        # Extract affected components
        components = ticket_input.get("components", [])

        # Extract urgency indicators
        urgency_keywords = ['urgent', 'critical', 'blocking', 'production', 'down', 'immediately']
        urgency_indicators = [kw for kw in urgency_keywords if kw in full_text.lower()]

        state["technical_context"] = {
            "error_codes": error_codes,
            "stack_trace": stack_trace,
            "components": components,
            "urgency_indicators": urgency_indicators,
            "full_text": full_text
        }

        self.logger.info(f"Extracted: {len(error_codes)} error codes, stack_trace={'Yes' if stack_trace else 'No'}")

        return state

    async def invoke_claude_code_node(self, state: TroubleshootState) -> TroubleshootState:
        """
        Execute Claude Code analysis with timeout and fallback.

        Steps:
        1. Build focused prompt with technical context
        2. Execute ClaudeCodeClient.analyze_issue()
        3. Handle timeout (fallback to LLM-only)
        4. Parse structured response
        """
        self.logger.info("Invoking Claude Code for codebase analysis")

        ticket_input = state["ticket_input"]
        tech_context = state["technical_context"]

        try:
            # Prepare error details for Claude Code
            error_details = {
                "error_code": ", ".join(tech_context["error_codes"]) if tech_context["error_codes"] else "N/A",
                "stack_trace": tech_context.get("stack_trace", "N/A"),
                "component": ", ".join(tech_context["components"]) if tech_context["components"] else "Unknown"
            }

            # Invoke Claude Code
            result: CodeAnalysisResult = await self.claude_code_client.analyze_issue(
                issue_description=ticket_input.get("title", ""),
                error_details=error_details
            )

            # Convert to dict
            state["code_analysis"] = {
                "is_valid_bug": result.is_valid_bug,
                "confidence": result.confidence,
                "root_cause": result.root_cause,
                "affected_files": result.affected_files,
                "workaround": result.workaround,
                "fix_recommendation": result.fix_recommendation,
                "estimated_effort_hours": result.estimated_effort_hours,
                "analysis_method": result.analysis_method,
                "error": result.error
            }

            self.logger.info(
                f"Analysis complete: method={result.analysis_method}, "
                f"confidence={result.confidence:.2f}, is_bug={result.is_valid_bug}"
            )

        except Exception as e:
            self.logger.error(f"Claude Code invocation failed: {str(e)}", exc_info=True)
            state["errors"].append(f"Claude Code failed: {str(e)}")

            # Set default analysis
            state["code_analysis"] = {
                "is_valid_bug": False,
                "confidence": 0.3,
                "root_cause": "Unable to analyze",
                "affected_files": [],
                "workaround": None,
                "fix_recommendation": "Manual investigation required",
                "estimated_effort_hours": 0,
                "analysis_method": "error",
                "error": str(e)
            }

        return state

    async def check_workarounds_node(self, state: TroubleshootState) -> TroubleshootState:
        """
        Search for immediate workarounds.

        Sources:
        1. KB Search Agent results (if available in context)
        2. Similar resolved Jira tickets
        3. LLM synthesis of potential workarounds
        """
        self.logger.info("Checking for workarounds")

        workarounds = []
        context = state["context"]
        code_analysis = state["code_analysis"]

        # 1. Check KB Search Agent results
        kb_workarounds = self._get_kb_search_workarounds(context)
        if kb_workarounds:
            workarounds.extend(kb_workarounds)
            self.logger.info(f"Found {len(kb_workarounds)} workarounds from KB Search")

        # 2. Search similar Jira tickets
        similar_workarounds = await self._search_similar_tickets(state)
        if similar_workarounds:
            workarounds.extend(similar_workarounds)
            self.logger.info(f"Found {len(similar_workarounds)} workarounds from similar tickets")

        # 3. Add Claude Code workaround if available
        if code_analysis.get("workaround"):
            workarounds.append({
                "description": code_analysis["workaround"],
                "type": "code_analysis",
                "source": "Claude Code",
                "confidence": code_analysis.get("confidence", 0.5)
            })

        state["workarounds"] = workarounds
        self.logger.info(f"Total workarounds found: {len(workarounds)}")

        return state

    def _get_kb_search_workarounds(self, context: WorkflowContext) -> List[Dict[str, Any]]:
        """Extract workarounds from KB Search Agent results."""
        stage2_results = context.stage_results.get("stage2", [])
        kb_result = next(
            (r for r in stage2_results if r.agent_name == "kb_search"),
            None
        )

        if kb_result and kb_result.status == AgentStatus.COMPLETED:
            articles = kb_result.output.get("relevant_articles", [])
            return [
                {
                    "description": f"See KB article: {article.get('title')}",
                    "type": "knowledge_base",
                    "source": f"KB-{article.get('id')}",
                    "confidence": article.get("score", 0.7)
                }
                for article in articles[:3]  # Top 3
            ]

        return []

    async def _search_similar_tickets(self, state: TroubleshootState) -> List[Dict[str, Any]]:
        """Search for similar resolved tickets in Jira."""
        try:
            tech_context = state["technical_context"]
            error_codes = tech_context.get("error_codes", [])

            if not error_codes:
                return []

            # Build JQL query
            error_query = " OR ".join([f'text ~ "{code}"' for code in error_codes[:3]])
            jql = f'status = Resolved AND ({error_query})'

            # Search Jira
            results = self.jira_client.search_issues(jql, max_results=5)

            workarounds = []
            for issue in results:
                resolution = issue.get('resolution', {}).get('name', '')
                if resolution:
                    workarounds.append({
                        "description": f"Similar issue {issue['key']}: {resolution}",
                        "type": "similar_ticket",
                        "source": issue['key'],
                        "confidence": 0.6
                    })

            return workarounds

        except Exception as e:
            self.logger.error(f"Failed to search similar tickets: {str(e)}")
            return []

    def validate_bug_node(self, state: TroubleshootState) -> TroubleshootState:
        """
        Assess confidence and determine if auto-ticket creation should proceed.

        Logic:
        - Must be is_valid_bug == True
        - Confidence >= BUG_CONFIDENCE_THRESHOLD (0.75)
        - AUTO_CREATE_BUG_TICKETS config enabled
        """
        self.logger.info("Validating bug and determining auto-ticket creation")

        code_analysis = state["code_analysis"]

        is_valid_bug = code_analysis.get("is_valid_bug", False)
        confidence = code_analysis.get("confidence", 0.0)

        should_create = (
            Config.AUTO_CREATE_BUG_TICKETS
            and is_valid_bug
            and confidence >= Config.BUG_CONFIDENCE_THRESHOLD
        )

        needs_human_review = (
            is_valid_bug
            and confidence < Config.BUG_CONFIDENCE_THRESHOLD
        )

        state["bug_validation"] = {
            "should_create": should_create,
            "needs_human_review": needs_human_review,
            "confidence": confidence,
            "threshold": Config.BUG_CONFIDENCE_THRESHOLD,
            "reason": self._get_validation_reason(is_valid_bug, confidence, should_create)
        }

        self.logger.info(
            f"Validation: should_create={should_create}, "
            f"needs_review={needs_human_review}, confidence={confidence:.2f}"
        )

        return state

    def _get_validation_reason(self, is_valid_bug: bool, confidence: float, should_create: bool) -> str:
        """Get human-readable validation reason."""
        if not is_valid_bug:
            return "Not identified as a valid bug"
        elif not Config.AUTO_CREATE_BUG_TICKETS:
            return "Auto-ticket creation disabled in config"
        elif confidence < Config.BUG_CONFIDENCE_THRESHOLD:
            return f"Confidence {confidence:.2f} below threshold {Config.BUG_CONFIDENCE_THRESHOLD}"
        elif should_create:
            return f"High confidence ({confidence:.2f}) - auto-creating ticket"
        else:
            return "Unknown reason"

    async def create_jira_ticket_node(self, state: TroubleshootState) -> TroubleshootState:
        """
        Conditionally create bug ticket based on validation.

        Only executes if:
        - confidence >= threshold
        - is_valid_bug == True
        - config flag enabled

        Actions:
        1. Create bug ticket with structured description
        2. Link to original support ticket
        3. Add comment to support ticket
        4. Apply auto-labels
        """
        self.logger.info("Creating Jira bug ticket")

        # This node should only execute if should_create == True
        # (enforced by conditional edge in workflow)

        code_analysis = state["code_analysis"]
        ticket_input = state["ticket_input"]
        workarounds = state["workarounds"]
        context = state["context"]

        try:
            # Build bug ticket description
            description = self._build_bug_ticket_description(
                code_analysis,
                ticket_input,
                workarounds,
                context,
                state["metadata"]
            )

            # Determine priority
            priority = self._map_severity_to_priority(
                code_analysis.get("severity", "medium"),
                context
            )

            # Create ticket
            bug_ticket = self.jira_client.create_issue(
                project=Config.JIRA_URL.split("//")[1].split(".")[0].upper(),  # Extract project key
                issue_type="Bug",
                summary=f"[Auto-detected] {code_analysis['root_cause'][:100]}",
                description=description,
                priority=priority,
                labels=[
                    "auto-detected",
                    "technical-troubleshoot-agent",
                    "claude-code-analysis",
                    f"confidence-{'high' if code_analysis['confidence'] > 0.85 else 'medium'}"
                ]
            )

            bug_key = bug_ticket.get("key")

            # Link to original ticket
            self.jira_client.link_issues(
                inward_issue=context.ticket_id,
                outward_issue=bug_key,
                link_type="is caused by"
            )

            # Add comment to support ticket
            await self._add_support_ticket_comment(context.ticket_id, bug_key, code_analysis, workarounds)

            state["jira_ticket"] = {
                "created": True,
                "ticket_key": bug_key,
                "link": f"{Config.JIRA_URL}/browse/{bug_key}",
                "linked_to_support_ticket": context.ticket_id,
                "priority": priority
            }

            self.logger.info(f"Bug ticket created: {bug_key}")

        except Exception as e:
            self.logger.error(f"Failed to create Jira ticket: {str(e)}", exc_info=True)
            state["errors"].append(f"Jira ticket creation failed: {str(e)}")
            state["jira_ticket"] = {
                "created": False,
                "error": str(e)
            }

        return state

    def _build_bug_ticket_description(
        self,
        code_analysis: Dict[str, Any],
        ticket_input: Dict[str, Any],
        workarounds: List[Dict[str, Any]],
        context: WorkflowContext,
        metadata: Dict[str, Any]
    ) -> str:
        """Build structured bug ticket description."""

        affected_files_str = "\n".join([
            f"- **File**: `{f['file']}` (Line {f.get('line', 'N/A')})\n  **Issue**: {f.get('issue', 'See analysis')}"
            for f in code_analysis.get("affected_files", [])
        ])

        workarounds_str = "\n".join([
            f"{i+1}. {w['description']} (Source: {w.get('source', 'Unknown')})"
            for i, w in enumerate(workarounds[:3])
        ])

        description = f"""🤖 **Auto-detected by Technical Troubleshooting Agent**

## Root Cause
{code_analysis['root_cause']}

## Affected Components
{affected_files_str if affected_files_str else "No specific files identified"}

## Error Details
- **Error Code**: {ticket_input.get('error_code', 'N/A')}
- **Impact Scale**: {ticket_input.get('impact_scale', 'Unknown')}
- **Business Criticality**: {ticket_input.get('criticality', 'Medium')}

## Recommended Fix
{code_analysis.get('fix_recommendation', 'Investigation required')}

**Estimated Effort**: {code_analysis.get('estimated_effort_hours', 0)} hours

## Workarounds Available
{workarounds_str if workarounds_str else "No workarounds identified"}

## Original Support Ticket
[{context.ticket_id}]({Config.JIRA_URL}/browse/{context.ticket_id})

---
*Confidence Score: {code_analysis['confidence']:.2f}*
*Analysis Method: {code_analysis.get('analysis_method', 'Unknown')}*
*Generated: {metadata.get('start_time', 'N/A')}*
"""
        return description

    def _map_severity_to_priority(self, severity: str, context: WorkflowContext) -> str:
        """Map severity + customer tier to Jira priority."""
        base_mapping = {
            "critical": "Highest",
            "high": "High",
            "medium": "Medium",
            "low": "Low"
        }

        priority = base_mapping.get(severity.lower(), "Medium")

        # Boost priority for enterprise customers (if customer context available)
        stage2_results = context.stage_results.get("stage2", [])
        customer_result = next(
            (r for r in stage2_results if r.agent_name == "customer_context"),
            None
        )

        if customer_result:
            tier = customer_result.output.get("tier", "standard")
            if tier == "enterprise" and priority == "High":
                priority = "Highest"

        return priority

    def _add_support_ticket_comment(
        self,
        ticket_id: str,
        bug_key: str,
        code_analysis: Dict[str, Any],
        workarounds: List[Dict[str, Any]]
    ):
        """Add comment to original support ticket."""
        try:
            workarounds_list = "\n".join([
                f"{i+1}. {w['description']}"
                for i, w in enumerate(workarounds[:3])
            ])

            comment = f"""🤖 **Technical Analysis Complete**

**Root Cause Identified**: {code_analysis['root_cause']}

**Bug Ticket Created**: [{bug_key}]({Config.JIRA_URL}/browse/{bug_key})

**Workarounds Available**:
{workarounds_list if workarounds_list else "None identified - see bug ticket for details"}

**Next Steps**:
- Engineering team has been notified via {bug_key}
- Estimated fix time: {code_analysis.get('estimated_effort_hours', 0)} hours

*This analysis was performed by the Technical Troubleshooting Agent with {code_analysis['confidence']:.0%} confidence using {code_analysis.get('analysis_method', 'code analysis')}.*
"""

            self.jira_client.add_comment(ticket_id, comment)
            self.logger.info(f"Added comment to support ticket {ticket_id}")

        except Exception as e:
            self.logger.error(f"Failed to add comment to {ticket_id}: {str(e)}")

    def format_output_node(self, state: TroubleshootState) -> TroubleshootState:
        """
        Compile final structured output.

        Output Structure:
        {
          "bug_analysis": {...},
          "workarounds": [...],
          "recommended_fix": {...},
          "jira_bug_ticket": {...},
          "similar_issues": [...],
          "needs_escalation": bool
        }
        """
        self.logger.info("Formatting final output")

        code_analysis = state["code_analysis"]
        workarounds = state["workarounds"]
        jira_ticket = state.get("jira_ticket")
        bug_validation = state["bug_validation"]

        state["final_output"] = {
            "ticket_id": state["ticket_input"].get("ticket_id"),
            "analysis_method": code_analysis.get("analysis_method"),
            "execution_time": state["metadata"].get("execution_time", 0),

            "bug_analysis": {
                "is_valid_bug": code_analysis.get("is_valid_bug"),
                "confidence": code_analysis.get("confidence"),
                "root_cause": code_analysis.get("root_cause"),
                "affected_components": code_analysis.get("affected_files", []),
                "error_pattern": ", ".join(state["technical_context"].get("error_codes", []))
            },

            "workarounds": workarounds,

            "recommended_fix": {
                "summary": code_analysis.get("fix_recommendation"),
                "estimated_effort": f"{code_analysis.get('estimated_effort_hours', 0)} hours"
            },

            "jira_bug_ticket": jira_ticket or {"created": False},

            "needs_escalation": bug_validation.get("needs_human_review", False),
            "escalation_reason": bug_validation.get("reason") if bug_validation.get("needs_human_review") else None,

            # For downstream agents (Root Cause Analysis)
            "for_root_cause_agent": {
                "affected_files": code_analysis.get("affected_files", []),
                "error_patterns": state["technical_context"].get("error_codes", []),
                "confidence": code_analysis.get("confidence"),
                "analysis_method": code_analysis.get("analysis_method")
            }
        }

        self.logger.info("Output formatted successfully")

        return state


# ============================================================================
# Troubleshooting Workflow
# ============================================================================

class TroubleshootingWorkflow:
    """LangGraph workflow for technical troubleshooting."""

    def __init__(
        self,
        llm,
        claude_code_client: ClaudeCodeClient,
        jira_client: JiraClient
    ):
        self.llm = llm
        self.claude_code_client = claude_code_client
        self.jira_client = jira_client
        self.nodes = TroubleshootingWorkflowNodes(llm, claude_code_client, jira_client)
        self.logger = get_logger(__name__)
        self.workflow = self._build_workflow()

    def _build_workflow(self) -> StateGraph:
        """Build the LangGraph workflow."""
        workflow = StateGraph(TroubleshootState)

        # Add nodes
        workflow.add_node("extract_technical_details", self.nodes.extract_technical_details_node)
        workflow.add_node("invoke_claude_code", self.nodes.invoke_claude_code_node)
        workflow.add_node("check_workarounds", self.nodes.check_workarounds_node)
        workflow.add_node("validate_bug", self.nodes.validate_bug_node)
        workflow.add_node("create_jira_ticket", self.nodes.create_jira_ticket_node)
        workflow.add_node("format_output", self.nodes.format_output_node)

        # Define flow
        workflow.set_entry_point("extract_technical_details")
        workflow.add_edge("extract_technical_details", "invoke_claude_code")
        workflow.add_edge("invoke_claude_code", "check_workarounds")
        workflow.add_edge("check_workarounds", "validate_bug")

        # Conditional edge: create ticket only if should_create == True
        workflow.add_conditional_edges(
            "validate_bug",
            self._should_create_ticket,
            {
                "create_ticket": "create_jira_ticket",
                "skip_ticket": "format_output"
            }
        )

        workflow.add_edge("create_jira_ticket", "format_output")
        workflow.add_edge("format_output", END)

        return workflow.compile()

    def _should_create_ticket(self, state: TroubleshootState) -> str:
        """Determine if ticket creation should proceed."""
        should_create = state["bug_validation"].get("should_create", False)
        return "create_ticket" if should_create else "skip_ticket"

    async def process(self, context: WorkflowContext) -> Dict[str, Any]:
        """Process a ticket through the troubleshooting workflow."""
        import time
        start_time = time.time()

        # Extract ticket data from context
        issue_key = context.ticket_id
        jira_issue = self.jira_client.get_issue(issue_key)

        if not jira_issue:
            raise ValueError(f"Failed to fetch Jira issue: {issue_key}")

        ticket_input = {
            "ticket_id": jira_issue.get('key', issue_key),
            "title": jira_issue.get('summary', ""),
            "description": jira_issue.get('description', ""),
            "components": jira_issue.get('components', []),
            "labels": jira_issue.get('labels', [])
        }

        initial_state = {
            "ticket_input": ticket_input,
            "context": context,
            "technical_context": {},
            "code_analysis": {},
            "workarounds": [],
            "bug_validation": {},
            "jira_ticket": None,
            "final_output": None,
            "errors": [],
            "metadata": {"start_time": time.time()}
        }

        result = await self.workflow.ainvoke(initial_state)

        # Calculate execution time
        execution_time = time.time() - start_time
        if result.get("final_output"):
            result["final_output"]["execution_time"] = execution_time

        if result["final_output"]:
            return result["final_output"]
        else:
            raise ValueError(f"Troubleshooting failed with errors: {result['errors']}")


# ============================================================================
# Main Technical Troubleshooting Agent
# ============================================================================

@register_agent(stage="stage2", name="technical_troubleshoot")
class TechnicalTroubleshootAgent(BaseAgent):
    """
    Technical Troubleshooting Agent - Claude Code Integration.

    Uses LangGraph workflow to:
    1. Extract technical details from tickets
    2. Invoke Claude Code for deep codebase analysis
    3. Search for workarounds
    4. Validate bugs with confidence scoring
    5. Auto-create Jira bug tickets (if confidence >= 0.75)
    6. Provide structured troubleshooting output
    """

    def __init__(self, name="technical_troubleshoot"):
        """Initialize the technical troubleshooting agent."""
        super().__init__(name)
        self.llm = create_anthropic_llm(temperature=0.1, max_tokens=2000)
        self.claude_code_client = ClaudeCodeClient()
        self.jira_client = JiraClient()
        self.workflow = TroubleshootingWorkflow(
            self.llm,
            self.claude_code_client,
            self.jira_client
        )

    async def should_execute(self, context: WorkflowContext) -> bool:
        """Execute only for technical issues."""
        stage1_results = context.stage_results.get("stage1", [])
        triage_result = next(
            (r for r in stage1_results if r.agent_name == "triage"),
            None
        )

        if triage_result and triage_result.output.get("category") in ["bug", "Technical"]:
            logger.info(f"[{context.correlation_id}] Executing for technical issue")
            return True

        logger.info(f"[{context.correlation_id}] Skipping - not a technical issue")
        return False

    async def process(self, context: WorkflowContext) -> Dict[str, Any]:
        """
        Process a ticket through troubleshooting workflow.

        Args:
            context: WorkflowContext containing ticket data

        Returns:
            Dict with troubleshooting results
        """
        try:
            result = await self.workflow.process(context)
            return result

        except Exception as e:
            logger.error(
                f"Troubleshooting failed for {context.ticket_id}: {str(e)}",
                exc_info=True
            )
            raise
