"""Unit tests for PostgresAnalyticsClient using mocked database connections."""
import pytest
from unittest.mock import Mock, MagicMock, patch, call
from datetime import datetime
from psycopg2.pool import SimpleConnectionPool

from app.models.integrations.postgres_analytics_client import PostgresAnalyticsClient


class TestPostgresAnalyticsClientInitialization:
    """Test client initialization and connection pooling."""

    @patch('app.models.integrations.postgres_analytics_client.SimpleConnectionPool')
    def test_client_initialization_success(self, mock_pool_class):
        """Test successful client initialization with connection pool."""
        mock_pool = MagicMock()
        mock_pool_class.return_value = mock_pool

        client = PostgresAnalyticsClient()

        assert client is not None
        assert client.pool == mock_pool
        mock_pool_class.assert_called_once()

    @patch('app.models.integrations.postgres_analytics_client.SimpleConnectionPool')
    def test_client_initialization_uses_config(self, mock_pool_class):
        """Test that initialization uses Config values."""
        mock_pool = MagicMock()
        mock_pool_class.return_value = mock_pool

        with patch('app.models.integrations.postgres_analytics_client.Config') as mock_config:
            mock_config.POSTGRESQL_HOST = 'test_host'
            mock_config.POSTGRESQL_PORT = 5433
            mock_config.POSTGRESQL_USER = 'test_user'
            mock_config.POSTGRESQL_PASSWORD = 'test_pass'
            mock_config.POSTGRESQL_DATABASE = 'test_db'

            client = PostgresAnalyticsClient()

            # Verify pool was created with correct parameters
            call_args = mock_pool_class.call_args
            assert "host='test_host'" in str(call_args) or 'host=test_host' in str(call_args)


class TestInsertWorkflowExecution:
    """Test insert_workflow_execution method."""

    @patch('app.models.integrations.postgres_analytics_client.SimpleConnectionPool')
    def test_insert_workflow_execution_success(self, mock_pool_class):
        """Test successful workflow execution insertion."""
        # Setup mocks
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_cursor = MagicMock()

        mock_pool.getconn.return_value = mock_conn
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_pool_class.return_value = mock_pool

        client = PostgresAnalyticsClient()

        # Test data
        workflow_data = {
            'correlation_id': 'TEST-123-1234567890',
            'ticket_id': 'TEST-123',
            'workflow_start_time': datetime.now(),
            'workflow_status': 'running',
            'webhook_payload': {'key': 'value'}
        }

        result = client.insert_workflow_execution(**workflow_data)

        assert result is True
        mock_cursor.execute.assert_called_once()
        mock_conn.commit.assert_called_once()
        mock_pool.putconn.assert_called_once_with(mock_conn)

    @patch('app.models.integrations.postgres_analytics_client.SimpleConnectionPool')
    def test_insert_workflow_execution_with_all_fields(self, mock_pool_class):
        """Test workflow insertion with all optional fields."""
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_cursor = MagicMock()

        mock_pool.getconn.return_value = mock_conn
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_pool_class.return_value = mock_pool

        client = PostgresAnalyticsClient()

        workflow_data = {
            'correlation_id': 'TEST-123-1234567890',
            'ticket_id': 'TEST-123',
            'workflow_start_time': datetime.now(),
            'workflow_status': 'running',
            'stages_total': 4,
            'webhook_payload': {'key': 'value'}
        }

        result = client.insert_workflow_execution(**workflow_data)

        assert result is True
        # Verify SQL contains all fields
        sql_call = mock_cursor.execute.call_args[0][0]
        assert 'correlation_id' in sql_call
        assert 'stages_total' in sql_call

    @patch('app.models.integrations.postgres_analytics_client.SimpleConnectionPool')
    def test_insert_workflow_execution_handles_error(self, mock_pool_class):
        """Test graceful error handling during insertion."""
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_cursor = MagicMock()

        # Simulate database error
        mock_cursor.execute.side_effect = Exception("Database error")
        mock_pool.getconn.return_value = mock_conn
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_pool_class.return_value = mock_pool

        client = PostgresAnalyticsClient()

        workflow_data = {
            'correlation_id': 'TEST-123-1234567890',
            'ticket_id': 'TEST-123',
            'workflow_start_time': datetime.now(),
            'workflow_status': 'running'
        }

        # Should not raise exception, return False
        result = client.insert_workflow_execution(**workflow_data)

        assert result is False
        mock_conn.rollback.assert_called_once()
        mock_pool.putconn.assert_called_once_with(mock_conn)


class TestUpdateWorkflowExecution:
    """Test update_workflow_execution method."""

    @patch('app.models.integrations.postgres_analytics_client.SimpleConnectionPool')
    def test_update_workflow_execution_success(self, mock_pool_class):
        """Test successful workflow execution update."""
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_cursor = MagicMock()

        mock_pool.getconn.return_value = mock_conn
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_pool_class.return_value = mock_pool

        client = PostgresAnalyticsClient()

        update_data = {
            'correlation_id': 'TEST-123-1234567890',
            'workflow_end_time': datetime.now(),
            'total_execution_time_ms': 5000,
            'workflow_status': 'completed',
            'stages_completed': 4,
            'agents_executed': 10,
            'agents_completed': 9,
            'agents_failed': 0,
            'agents_skipped': 1
        }

        result = client.update_workflow_execution(**update_data)

        assert result is True
        mock_cursor.execute.assert_called_once()
        mock_conn.commit.assert_called_once()

    @patch('app.models.integrations.postgres_analytics_client.SimpleConnectionPool')
    def test_update_workflow_execution_partial_fields(self, mock_pool_class):
        """Test update with only some fields."""
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_cursor = MagicMock()

        mock_pool.getconn.return_value = mock_conn
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_pool_class.return_value = mock_pool

        client = PostgresAnalyticsClient()

        # Update only status and end time
        update_data = {
            'correlation_id': 'TEST-123-1234567890',
            'workflow_status': 'failed',
            'error_message': 'Something went wrong'
        }

        result = client.update_workflow_execution(**update_data)

        assert result is True
        sql_call = mock_cursor.execute.call_args[0][0]
        assert 'workflow_status' in sql_call
        assert 'error_message' in sql_call

    @patch('app.models.integrations.postgres_analytics_client.SimpleConnectionPool')
    def test_update_workflow_execution_handles_error(self, mock_pool_class):
        """Test error handling during update."""
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_cursor = MagicMock()

        mock_cursor.execute.side_effect = Exception("Update failed")
        mock_pool.getconn.return_value = mock_conn
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_pool_class.return_value = mock_pool

        client = PostgresAnalyticsClient()

        update_data = {
            'correlation_id': 'TEST-123-1234567890',
            'workflow_status': 'completed'
        }

        result = client.update_workflow_execution(**update_data)

        assert result is False
        mock_conn.rollback.assert_called_once()


class TestInsertAgentExecution:
    """Test insert_agent_execution method."""

    @patch('app.models.integrations.postgres_analytics_client.SimpleConnectionPool')
    def test_insert_agent_execution_success(self, mock_pool_class):
        """Test successful agent execution insertion."""
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_cursor = MagicMock()

        mock_pool.getconn.return_value = mock_conn
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_pool_class.return_value = mock_pool

        client = PostgresAnalyticsClient()

        agent_data = {
            'correlation_id': 'TEST-123-1234567890',
            'ticket_id': 'TEST-123',
            'agent_name': 'sentiment',
            'stage_name': 'stage1',
            'start_time': datetime.now(),
            'end_time': datetime.now(),
            'execution_time_ms': 150,
            'agent_status': 'completed',
            'should_execute_result': True,
            'output_data': {'sentiment_score': 0.8}
        }

        result = client.insert_agent_execution(**agent_data)

        assert result is True
        mock_cursor.execute.assert_called_once()
        mock_conn.commit.assert_called_once()

    @patch('app.models.integrations.postgres_analytics_client.SimpleConnectionPool')
    def test_insert_agent_execution_with_execution_order(self, mock_pool_class):
        """Test agent insertion with execution order."""
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_cursor = MagicMock()

        mock_pool.getconn.return_value = mock_conn
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_pool_class.return_value = mock_pool

        client = PostgresAnalyticsClient()

        agent_data = {
            'correlation_id': 'TEST-123-1234567890',
            'ticket_id': 'TEST-123',
            'agent_name': 'triage',
            'stage_name': 'stage1',
            'execution_order': 1,
            'start_time': datetime.now(),
            'agent_status': 'completed'
        }

        result = client.insert_agent_execution(**agent_data)

        assert result is True
        sql_call = mock_cursor.execute.call_args[0][0]
        assert 'execution_order' in sql_call

    @patch('app.models.integrations.postgres_analytics_client.SimpleConnectionPool')
    def test_insert_agent_execution_skipped_status(self, mock_pool_class):
        """Test inserting skipped agent execution."""
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_cursor = MagicMock()

        mock_pool.getconn.return_value = mock_conn
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_pool_class.return_value = mock_pool

        client = PostgresAnalyticsClient()

        agent_data = {
            'correlation_id': 'TEST-123-1234567890',
            'ticket_id': 'TEST-123',
            'agent_name': 'auto_response',
            'stage_name': 'stage2',
            'start_time': datetime.now(),
            'agent_status': 'skipped',
            'should_execute_result': False,
            'execution_time_ms': 0
        }

        result = client.insert_agent_execution(**agent_data)

        assert result is True

    @patch('app.models.integrations.postgres_analytics_client.SimpleConnectionPool')
    def test_insert_agent_execution_with_error(self, mock_pool_class):
        """Test inserting failed agent execution with error message."""
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_cursor = MagicMock()

        mock_pool.getconn.return_value = mock_conn
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_pool_class.return_value = mock_pool

        client = PostgresAnalyticsClient()

        agent_data = {
            'correlation_id': 'TEST-123-1234567890',
            'ticket_id': 'TEST-123',
            'agent_name': 'escalation',
            'stage_name': 'stage1',
            'start_time': datetime.now(),
            'agent_status': 'failed',
            'error_message': 'API timeout',
            'execution_time_ms': 30000
        }

        result = client.insert_agent_execution(**agent_data)

        assert result is True
        sql_call = mock_cursor.execute.call_args[0][0]
        assert 'error_message' in sql_call

    @patch('app.models.integrations.postgres_analytics_client.SimpleConnectionPool')
    def test_insert_agent_execution_handles_error(self, mock_pool_class):
        """Test graceful error handling during agent insertion."""
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_cursor = MagicMock()

        mock_cursor.execute.side_effect = Exception("Insert failed")
        mock_pool.getconn.return_value = mock_conn
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_pool_class.return_value = mock_pool

        client = PostgresAnalyticsClient()

        agent_data = {
            'correlation_id': 'TEST-123-1234567890',
            'ticket_id': 'TEST-123',
            'agent_name': 'sentiment',
            'stage_name': 'stage1',
            'start_time': datetime.now(),
            'agent_status': 'completed'
        }

        result = client.insert_agent_execution(**agent_data)

        assert result is False
        mock_conn.rollback.assert_called_once()


class TestGetWorkflowStats:
    """Test get_workflow_stats method."""

    @patch('app.models.integrations.postgres_analytics_client.SimpleConnectionPool')
    def test_get_workflow_stats_by_correlation_id(self, mock_pool_class):
        """Test retrieving workflow stats by correlation_id."""
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_cursor = MagicMock()

        # Mock query result
        mock_cursor.fetchone.return_value = {
            'correlation_id': 'TEST-123-1234567890',
            'ticket_id': 'TEST-123',
            'workflow_status': 'completed',
            'total_execution_time_ms': 5000,
            'agents_executed': 10,
            'agents_completed': 9,
            'agents_failed': 0,
            'agents_skipped': 1
        }

        mock_pool.getconn.return_value = mock_conn
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_pool_class.return_value = mock_pool

        client = PostgresAnalyticsClient()

        result = client.get_workflow_stats(correlation_id='TEST-123-1234567890')

        assert result is not None
        assert result['correlation_id'] == 'TEST-123-1234567890'
        assert result['workflow_status'] == 'completed'
        assert result['agents_executed'] == 10

    @patch('app.models.integrations.postgres_analytics_client.SimpleConnectionPool')
    def test_get_workflow_stats_by_ticket_id(self, mock_pool_class):
        """Test retrieving workflow stats by ticket_id."""
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_cursor = MagicMock()

        mock_cursor.fetchall.return_value = [
            {'correlation_id': 'TEST-123-1234567890', 'workflow_status': 'completed'},
            {'correlation_id': 'TEST-123-1234567891', 'workflow_status': 'failed'}
        ]

        mock_pool.getconn.return_value = mock_conn
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_pool_class.return_value = mock_pool

        client = PostgresAnalyticsClient()

        results = client.get_workflow_stats(ticket_id='TEST-123')

        assert len(results) == 2
        assert results[0]['workflow_status'] == 'completed'

    @patch('app.models.integrations.postgres_analytics_client.SimpleConnectionPool')
    def test_get_workflow_stats_not_found(self, mock_pool_class):
        """Test querying non-existent workflow."""
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_cursor = MagicMock()

        mock_cursor.fetchone.return_value = None

        mock_pool.getconn.return_value = mock_conn
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_pool_class.return_value = mock_pool

        client = PostgresAnalyticsClient()

        result = client.get_workflow_stats(correlation_id='NONEXISTENT')

        assert result is None

    @patch('app.models.integrations.postgres_analytics_client.SimpleConnectionPool')
    def test_get_workflow_stats_handles_error(self, mock_pool_class):
        """Test error handling in get_workflow_stats."""
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_cursor = MagicMock()

        mock_cursor.execute.side_effect = Exception("Query failed")
        mock_pool.getconn.return_value = mock_conn
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_pool_class.return_value = mock_pool

        client = PostgresAnalyticsClient()

        result = client.get_workflow_stats(correlation_id='TEST-123')

        assert result is None


class TestConnectionPooling:
    """Test connection pool behavior."""

    @patch('app.models.integrations.postgres_analytics_client.SimpleConnectionPool')
    def test_connection_returned_to_pool(self, mock_pool_class):
        """Test that connections are properly returned to pool."""
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_cursor = MagicMock()

        mock_pool.getconn.return_value = mock_conn
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_pool_class.return_value = mock_pool

        client = PostgresAnalyticsClient()

        workflow_data = {
            'correlation_id': 'TEST-123-1234567890',
            'ticket_id': 'TEST-123',
            'workflow_start_time': datetime.now(),
            'workflow_status': 'running'
        }

        client.insert_workflow_execution(**workflow_data)

        # Verify connection was returned
        mock_pool.putconn.assert_called_once_with(mock_conn)

    @patch('app.models.integrations.postgres_analytics_client.SimpleConnectionPool')
    def test_connection_returned_on_error(self, mock_pool_class):
        """Test that connections are returned even on error."""
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_cursor = MagicMock()

        mock_cursor.execute.side_effect = Exception("Error")
        mock_pool.getconn.return_value = mock_conn
        mock_conn.cursor.return_value.__enter__.return_value = mock_cursor
        mock_pool_class.return_value = mock_pool

        client = PostgresAnalyticsClient()

        workflow_data = {
            'correlation_id': 'TEST-123-1234567890',
            'ticket_id': 'TEST-123',
            'workflow_start_time': datetime.now(),
            'workflow_status': 'running'
        }

        client.insert_workflow_execution(**workflow_data)

        # Verify connection was returned even after error
        mock_pool.putconn.assert_called_once_with(mock_conn)

    @patch('app.models.integrations.postgres_analytics_client.SimpleConnectionPool')
    def test_close_all_connections(self, mock_pool_class):
        """Test closing all connections in pool."""
        mock_pool = MagicMock()
        mock_pool_class.return_value = mock_pool

        client = PostgresAnalyticsClient()
        client.close()

        mock_pool.closeall.assert_called_once()


class TestGracefulDegradation:
    """Test that analytics failures don't crash the application."""

    @patch('app.models.integrations.postgres_analytics_client.SimpleConnectionPool')
    def test_init_failure_is_logged_not_raised(self, mock_pool_class):
        """Test that initialization failures are logged but don't raise."""
        mock_pool_class.side_effect = Exception("Connection failed")

        # Should not raise exception
        client = PostgresAnalyticsClient()

        assert client is not None
        assert client.pool is None

    @patch('app.models.integrations.postgres_analytics_client.SimpleConnectionPool')
    def test_operations_fail_gracefully_when_no_pool(self, mock_pool_class):
        """Test operations when pool is not available."""
        mock_pool_class.side_effect = Exception("Connection failed")

        client = PostgresAnalyticsClient()

        # All operations should return False but not crash
        result = client.insert_workflow_execution(
            correlation_id='TEST-123',
            ticket_id='TEST-123',
            workflow_start_time=datetime.now(),
            workflow_status='running'
        )

        assert result is False
