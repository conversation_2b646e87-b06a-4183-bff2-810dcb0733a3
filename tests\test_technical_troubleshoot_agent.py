"""
Unit tests for Technical Troubleshooting Agent.

Tests all workflow nodes, state transitions, conditional logic,
agent coordination, and Jira integration.
"""
import asyncio
import pytest
from unittest.mock import AsyncMock, MagicMock, patch, call
from typing import Dict, Any

from app.agents.customer_support.technical_troubleshoot_agent import (
    TechnicalTroubleshootAgent,
    TroubleshootingWorkflow,
    TroubleshootingWorkflowNodes,
    TroubleshootState
)
from app.agents.customer_support.base_agent import WorkflowContext, AgentResult, AgentStatus
from app.models.integrations.claude_code_client import CodeAnalysisResult


# ============================================================================
# Fixtures
# ============================================================================

@pytest.fixture
def mock_llm():
    """Mock LLM for testing."""
    llm = MagicMock()
    llm.invoke = MagicMock(return_value=MagicMock(content='{"result": "mock"}'))
    return llm


@pytest.fixture
def mock_claude_code_client():
    """Mock Claude Code client."""
    client = MagicMock()
    client.analyze_issue = AsyncMock()
    return client


@pytest.fixture
def mock_jira_client():
    """Mock Jira client."""
    client = MagicMock()
    client.get_issue = MagicMock()
    client.create_issue = MagicMock()
    client.link_issues = MagicMock()
    client.add_comment = MagicMock()
    client.search_issues = MagicMock(return_value=[])
    return client


@pytest.fixture
def workflow_nodes(mock_llm, mock_claude_code_client, mock_jira_client):
    """Create workflow nodes instance."""
    return TroubleshootingWorkflowNodes(
        mock_llm,
        mock_claude_code_client,
        mock_jira_client
    )


@pytest.fixture
def sample_ticket_input():
    """Sample ticket input for testing."""
    return {
        "ticket_id": "SUPPORT-123",
        "title": "Database connection errors in production",
        "description": """We are experiencing HTTP 500 errors when connecting to the database.

Error: DATABASE_CONNECTION_TIMEOUT
Stack trace:
  File "app/db/connection.py", line 89, in get_connection
    conn = pool.get_connection(timeout=5)
  psycopg2.OperationalError: FATAL: remaining connection slots are reserved

This is URGENT and BLOCKING production traffic.""",
        "components": ["database", "api"],
        "labels": ["production", "critical"]
    }


@pytest.fixture
def sample_workflow_context():
    """Sample workflow context."""
    return WorkflowContext(
        ticket_id="SUPPORT-123",
        correlation_id="test-correlation-123",
        ticket_data={"key": "SUPPORT-123"},
        stage_results={
            "stage1": [
                AgentResult(
                    agent_name="triage",
                    status=AgentStatus.COMPLETED,
                    output={"category": "bug", "priority": "high"},
                    execution_time=1.0
                )
            ],
            "stage2": []
        },
        metadata={}
    )


@pytest.fixture
def initial_state(sample_ticket_input, sample_workflow_context):
    """Initial workflow state."""
    return {
        "ticket_input": sample_ticket_input,
        "context": sample_workflow_context,
        "technical_context": {},
        "code_analysis": {},
        "workarounds": [],
        "bug_validation": {},
        "jira_ticket": None,
        "final_output": None,
        "errors": [],
        "metadata": {"start_time": 1234567890}
    }


@pytest.fixture
def sample_code_analysis_result():
    """Sample Claude Code analysis result."""
    return CodeAnalysisResult(
        is_valid_bug=True,
        confidence=0.87,
        root_cause="Database connection pool exhausted under high load",
        affected_files=[
            {
                "file": "app/db/connection.py",
                "line": 89,
                "issue": "max_connections=10 too low for production"
            },
            {
                "file": "config/database.yaml",
                "line": 12,
                "issue": "Missing connection pool timeout"
            }
        ],
        workaround="Increase max_connections to 50 in config/database.yaml",
        fix_recommendation="Implement dynamic connection pooling with auto-scaling",
        estimated_effort_hours=4,
        analysis_method="claude_code"
    )


# ============================================================================
# Test Extract Technical Details Node
# ============================================================================

class TestExtractTechnicalDetailsNode:
    """Test technical detail extraction."""

    def test_extract_error_codes(self, workflow_nodes, initial_state):
        """Test extraction of error codes."""
        state = workflow_nodes.extract_technical_details_node(initial_state)

        tech_context = state["technical_context"]
        # The regex extracts the second group from pattern r'\b(HTTP\s*)?([45]\d{2}|ERROR_\w+)\b'
        # So "HTTP 500" will extract "500" and "ERROR_DATABASE_CONNECTION_TIMEOUT" will extract "ERROR_DATABASE_CONNECTION_TIMEOUT"
        # But "DATABASE_CONNECTION_TIMEOUT" alone won't match unless it starts with ERROR_
        assert len(tech_context["error_codes"]) >= 1
        assert "500" in tech_context["error_codes"]

    def test_extract_stack_trace(self, workflow_nodes, initial_state):
        """Test extraction of stack trace."""
        state = workflow_nodes.extract_technical_details_node(initial_state)

        tech_context = state["technical_context"]
        # The regex looks for patterns like "Stack trace:" followed by content
        assert tech_context["stack_trace"] is not None
        assert "Stack trace" in tech_context["stack_trace"]

    def test_extract_components(self, workflow_nodes, initial_state):
        """Test extraction of components."""
        state = workflow_nodes.extract_technical_details_node(initial_state)

        tech_context = state["technical_context"]
        assert "database" in tech_context["components"]
        assert "api" in tech_context["components"]

    def test_extract_urgency_indicators(self, workflow_nodes, initial_state):
        """Test extraction of urgency keywords."""
        state = workflow_nodes.extract_technical_details_node(initial_state)

        tech_context = state["technical_context"]
        assert "urgent" in tech_context["urgency_indicators"]
        assert "blocking" in tech_context["urgency_indicators"]
        assert "production" in tech_context["urgency_indicators"]

    def test_no_stack_trace(self, workflow_nodes, initial_state):
        """Test handling when no stack trace present."""
        initial_state["ticket_input"]["description"] = "Simple error message with no debugging information"

        state = workflow_nodes.extract_technical_details_node(initial_state)

        tech_context = state["technical_context"]
        assert tech_context["stack_trace"] is None


# ============================================================================
# Test Invoke Claude Code Node
# ============================================================================

class TestInvokeClaudeCodeNode:
    """Test Claude Code invocation."""

    @pytest.mark.asyncio
    async def test_successful_claude_code_invocation(
        self,
        workflow_nodes,
        initial_state,
        sample_code_analysis_result
    ):
        """Test successful Claude Code analysis."""
        # Setup
        initial_state["technical_context"] = {
            "error_codes": ["500"],
            "stack_trace": "...",
            "components": ["database"]
        }
        workflow_nodes.claude_code_client.analyze_issue.return_value = sample_code_analysis_result

        # Execute
        state = await workflow_nodes.invoke_claude_code_node(initial_state)

        # Assert
        code_analysis = state["code_analysis"]
        assert code_analysis["is_valid_bug"] is True
        assert code_analysis["confidence"] == 0.87
        assert code_analysis["analysis_method"] == "claude_code"
        assert len(code_analysis["affected_files"]) == 2
        workflow_nodes.claude_code_client.analyze_issue.assert_called_once()

    @pytest.mark.asyncio
    async def test_claude_code_failure_sets_default(self, workflow_nodes, initial_state):
        """Test fallback when Claude Code fails."""
        # Setup
        initial_state["technical_context"] = {
            "error_codes": ["500"],
            "stack_trace": None,
            "components": []
        }
        workflow_nodes.claude_code_client.analyze_issue.side_effect = Exception("Claude Code failed")

        # Execute
        state = await workflow_nodes.invoke_claude_code_node(initial_state)

        # Assert
        code_analysis = state["code_analysis"]
        assert code_analysis["is_valid_bug"] is False
        assert code_analysis["confidence"] == 0.3
        assert code_analysis["analysis_method"] == "error"
        assert "Claude Code failed" in state["errors"][0]

    @pytest.mark.asyncio
    async def test_error_details_formatting(self, workflow_nodes, initial_state, sample_code_analysis_result):
        """Test proper formatting of error details for Claude Code."""
        # Setup
        initial_state["technical_context"] = {
            "error_codes": ["500", "TIMEOUT"],
            "stack_trace": "Stack trace here",
            "components": ["database", "api"]
        }
        workflow_nodes.claude_code_client.analyze_issue.return_value = sample_code_analysis_result

        # Execute
        await workflow_nodes.invoke_claude_code_node(initial_state)

        # Assert - check call arguments
        call_args = workflow_nodes.claude_code_client.analyze_issue.call_args
        error_details = call_args[1]["error_details"]
        assert "500, TIMEOUT" in error_details["error_code"]
        assert "database, api" in error_details["component"]


# ============================================================================
# Test Check Workarounds Node
# ============================================================================

class TestCheckWorkaroundsNode:
    """Test workaround detection."""

    @pytest.mark.asyncio
    async def test_workaround_from_claude_code(self, workflow_nodes, initial_state):
        """Test workaround extraction from Claude Code analysis."""
        # Setup
        initial_state["code_analysis"] = {
            "workaround": "Restart the service",
            "confidence": 0.8
        }

        # Execute
        state = await workflow_nodes.check_workarounds_node(initial_state)

        # Assert
        assert len(state["workarounds"]) > 0
        workaround = state["workarounds"][0]
        assert workaround["description"] == "Restart the service"
        assert workaround["type"] == "code_analysis"
        assert workaround["source"] == "Claude Code"

    @pytest.mark.asyncio
    async def test_workaround_from_kb_search(self, workflow_nodes, initial_state, sample_workflow_context):
        """Test workaround from KB Search Agent."""
        # Setup KB Search results
        sample_workflow_context.stage_results["stage2"] = [
            AgentResult(
                agent_name="kb_search",
                status=AgentStatus.COMPLETED,
                output={
                    "relevant_articles": [
                        {"id": "KB-001", "title": "Fix DB connection", "score": 0.9},
                        {"id": "KB-002", "title": "Pool config", "score": 0.8}
                    ]
                },
                execution_time=0.5
            )
        ]
        initial_state["context"] = sample_workflow_context
        initial_state["code_analysis"] = {}

        # Execute
        state = await workflow_nodes.check_workarounds_node(initial_state)

        # Assert
        kb_workarounds = [w for w in state["workarounds"] if w["type"] == "knowledge_base"]
        assert len(kb_workarounds) == 2
        assert "KB-001" in kb_workarounds[0]["source"]

    @pytest.mark.asyncio
    async def test_workaround_from_similar_tickets(self, workflow_nodes, initial_state):
        """Test workaround from similar Jira tickets."""
        # Setup
        initial_state["technical_context"] = {
            "error_codes": ["DATABASE_TIMEOUT"]
        }
        workflow_nodes.jira_client.search_issues.return_value = [
            {
                "key": "PROJ-456",
                "resolution": {"name": "Increased pool size"}
            }
        ]
        initial_state["code_analysis"] = {}

        # Execute
        state = await workflow_nodes.check_workarounds_node(initial_state)

        # Assert
        similar_workarounds = [w for w in state["workarounds"] if w["type"] == "similar_ticket"]
        assert len(similar_workarounds) == 1
        assert "PROJ-456" in similar_workarounds[0]["source"]

    @pytest.mark.asyncio
    async def test_no_workarounds_found(self, workflow_nodes, initial_state):
        """Test when no workarounds are found."""
        # Setup
        initial_state["technical_context"] = {"error_codes": []}
        initial_state["code_analysis"] = {}

        # Execute
        state = await workflow_nodes.check_workarounds_node(initial_state)

        # Assert
        assert len(state["workarounds"]) == 0


# ============================================================================
# Test Validate Bug Node
# ============================================================================

class TestValidateBugNode:
    """Test bug validation logic."""

    def test_should_create_high_confidence_bug(self, workflow_nodes, initial_state):
        """Test should create ticket for high confidence bug."""
        # Setup
        initial_state["code_analysis"] = {
            "is_valid_bug": True,
            "confidence": 0.90
        }

        with patch('config.settings.Config.AUTO_CREATE_BUG_TICKETS', True):
            with patch('config.settings.Config.BUG_CONFIDENCE_THRESHOLD', 0.75):
                # Execute
                state = workflow_nodes.validate_bug_node(initial_state)

        # Assert
        bug_validation = state["bug_validation"]
        assert bug_validation["should_create"] is True
        assert bug_validation["needs_human_review"] is False

    def test_should_not_create_low_confidence_bug(self, workflow_nodes, initial_state):
        """Test should NOT create ticket for low confidence bug."""
        # Setup
        initial_state["code_analysis"] = {
            "is_valid_bug": True,
            "confidence": 0.60  # Below 0.75 threshold
        }

        with patch('config.settings.Config.AUTO_CREATE_BUG_TICKETS', True):
            with patch('config.settings.Config.BUG_CONFIDENCE_THRESHOLD', 0.75):
                # Execute
                state = workflow_nodes.validate_bug_node(initial_state)

        # Assert
        bug_validation = state["bug_validation"]
        assert bug_validation["should_create"] is False
        assert bug_validation["needs_human_review"] is True
        assert "below threshold" in bug_validation["reason"]

    def test_should_not_create_when_not_valid_bug(self, workflow_nodes, initial_state):
        """Test should NOT create ticket when not a valid bug."""
        # Setup
        initial_state["code_analysis"] = {
            "is_valid_bug": False,
            "confidence": 0.95  # High confidence but not a bug
        }

        with patch('config.settings.Config.AUTO_CREATE_BUG_TICKETS', True):
            # Execute
            state = workflow_nodes.validate_bug_node(initial_state)

        # Assert
        bug_validation = state["bug_validation"]
        assert bug_validation["should_create"] is False
        assert "Not identified as a valid bug" in bug_validation["reason"]

    def test_should_not_create_when_disabled(self, workflow_nodes, initial_state):
        """Test should NOT create ticket when feature disabled."""
        # Setup
        initial_state["code_analysis"] = {
            "is_valid_bug": True,
            "confidence": 0.90
        }

        with patch('config.settings.Config.AUTO_CREATE_BUG_TICKETS', False):
            # Execute
            state = workflow_nodes.validate_bug_node(initial_state)

        # Assert
        bug_validation = state["bug_validation"]
        assert bug_validation["should_create"] is False

    def test_confidence_at_threshold(self, workflow_nodes, initial_state):
        """Test ticket creation when confidence exactly at threshold."""
        # Setup
        initial_state["code_analysis"] = {
            "is_valid_bug": True,
            "confidence": 0.75  # Exactly at threshold
        }

        with patch('config.settings.Config.AUTO_CREATE_BUG_TICKETS', True):
            with patch('config.settings.Config.BUG_CONFIDENCE_THRESHOLD', 0.75):
                # Execute
                state = workflow_nodes.validate_bug_node(initial_state)

        # Assert
        bug_validation = state["bug_validation"]
        assert bug_validation["should_create"] is True


# ============================================================================
# Test Create Jira Ticket Node
# ============================================================================

class TestCreateJiraTicketNode:
    """Test Jira ticket creation."""

    @pytest.mark.asyncio
    async def test_successful_ticket_creation(self, workflow_nodes, initial_state, sample_workflow_context):
        """Test successful bug ticket creation."""
        # Setup
        initial_state["code_analysis"] = {
            "root_cause": "Database pool exhausted",
            "confidence": 0.87,
            "affected_files": [{"file": "app/db.py", "line": 89}],
            "fix_recommendation": "Increase pool size",
            "estimated_effort_hours": 4,
            "analysis_method": "claude_code"
        }
        initial_state["workarounds"] = [
            {"description": "Restart service", "source": "KB-001"}
        ]
        workflow_nodes.jira_client.create_issue.return_value = {"key": "BUG-1234"}

        with patch('config.settings.Config.JIRA_URL', 'https://test.atlassian.net'):
            # Execute
            state = await workflow_nodes.create_jira_ticket_node(initial_state)

        # Assert
        assert state["jira_ticket"]["created"] is True
        assert state["jira_ticket"]["ticket_key"] == "BUG-1234"
        workflow_nodes.jira_client.create_issue.assert_called_once()
        workflow_nodes.jira_client.link_issues.assert_called_once()
        workflow_nodes.jira_client.add_comment.assert_called_once()

    @pytest.mark.asyncio
    async def test_ticket_description_format(self, workflow_nodes, initial_state, sample_workflow_context):
        """Test bug ticket description formatting."""
        # Setup
        initial_state["code_analysis"] = {
            "root_cause": "Test root cause",
            "confidence": 0.85,
            "affected_files": [
                {"file": "test.py", "line": 10, "issue": "Test issue"}
            ],
            "fix_recommendation": "Test fix",
            "estimated_effort_hours": 2,
            "analysis_method": "claude_code"
        }
        initial_state["workarounds"] = []
        workflow_nodes.jira_client.create_issue.return_value = {"key": "BUG-999"}

        with patch('config.settings.Config.JIRA_URL', 'https://test.atlassian.net'):
            # Execute
            await workflow_nodes.create_jira_ticket_node(initial_state)

        # Assert - check description content
        call_args = workflow_nodes.jira_client.create_issue.call_args
        description = call_args[1]["description"]
        assert "Test root cause" in description
        assert "test.py" in description
        assert "Line 10" in description
        assert "Confidence Score: 0.85" in description

    @pytest.mark.asyncio
    async def test_ticket_creation_failure(self, workflow_nodes, initial_state):
        """Test handling of ticket creation failure."""
        # Setup
        initial_state["code_analysis"] = {
            "root_cause": "Test",
            "confidence": 0.9,
            "affected_files": [],
            "fix_recommendation": "Fix it",
            "estimated_effort_hours": 1
        }
        initial_state["workarounds"] = []
        workflow_nodes.jira_client.create_issue.side_effect = Exception("Jira API error")

        # Execute
        state = await workflow_nodes.create_jira_ticket_node(initial_state)

        # Assert
        assert state["jira_ticket"]["created"] is False
        assert "error" in state["jira_ticket"]
        assert "Jira ticket creation failed" in state["errors"][0]

    @pytest.mark.asyncio
    async def test_priority_boost_for_enterprise_customer(self, workflow_nodes, initial_state, sample_workflow_context):
        """Test priority boost for enterprise customers."""
        # Setup customer context
        sample_workflow_context.stage_results["stage2"] = [
            AgentResult(
                agent_name="customer_context",
                status=AgentStatus.COMPLETED,
                output={"tier": "enterprise"},
                execution_time=0.5
            )
        ]
        initial_state["context"] = sample_workflow_context
        initial_state["code_analysis"] = {
            "root_cause": "Test",
            "confidence": 0.9,
            "severity": "high",
            "affected_files": [],
            "fix_recommendation": "Fix",
            "estimated_effort_hours": 1
        }
        initial_state["workarounds"] = []
        workflow_nodes.jira_client.create_issue.return_value = {"key": "BUG-001"}

        with patch('config.settings.Config.JIRA_URL', 'https://test.atlassian.net'):
            # Execute
            await workflow_nodes.create_jira_ticket_node(initial_state)

        # Assert - priority should be boosted to Highest
        call_args = workflow_nodes.jira_client.create_issue.call_args
        assert call_args[1]["priority"] == "Highest"


# ============================================================================
# Test Format Output Node
# ============================================================================

class TestFormatOutputNode:
    """Test output formatting."""

    def test_format_complete_output(self, workflow_nodes, initial_state):
        """Test formatting of complete output."""
        # Setup
        initial_state["technical_context"] = {"error_codes": ["500"]}
        initial_state["code_analysis"] = {
            "is_valid_bug": True,
            "confidence": 0.87,
            "root_cause": "Test root cause",
            "affected_files": [{"file": "test.py"}],
            "fix_recommendation": "Fix it",
            "estimated_effort_hours": 3,
            "analysis_method": "claude_code"
        }
        initial_state["workarounds"] = [
            {"description": "Workaround 1"}
        ]
        initial_state["jira_ticket"] = {
            "created": True,
            "ticket_key": "BUG-123"
        }
        initial_state["bug_validation"] = {
            "needs_human_review": False
        }

        # Execute
        state = workflow_nodes.format_output_node(initial_state)

        # Assert
        output = state["final_output"]
        assert output is not None
        assert output["bug_analysis"]["is_valid_bug"] is True
        assert output["bug_analysis"]["confidence"] == 0.87
        assert len(output["workarounds"]) == 1
        assert output["jira_bug_ticket"]["created"] is True
        assert output["needs_escalation"] is False
        assert "for_root_cause_agent" in output

    def test_format_output_with_escalation(self, workflow_nodes, initial_state):
        """Test output formatting when human review needed."""
        # Setup
        initial_state["technical_context"] = {"error_codes": []}
        initial_state["code_analysis"] = {
            "is_valid_bug": True,
            "confidence": 0.65,
            "root_cause": "Unclear",
            "affected_files": [],
            "fix_recommendation": "Investigate",
            "estimated_effort_hours": 0
        }
        initial_state["workarounds"] = []
        initial_state["jira_ticket"] = None
        initial_state["bug_validation"] = {
            "needs_human_review": True,
            "reason": "Low confidence"
        }

        # Execute
        state = workflow_nodes.format_output_node(initial_state)

        # Assert
        output = state["final_output"]
        assert output["needs_escalation"] is True
        assert output["escalation_reason"] == "Low confidence"
        assert output["jira_bug_ticket"]["created"] is False


# ============================================================================
# Test TechnicalTroubleshootAgent
# ============================================================================

class TestTechnicalTroubleshootAgent:
    """Test main agent class."""

    @pytest.mark.asyncio
    async def test_should_execute_for_bug_category(self, sample_workflow_context):
        """Test agent executes for bug category."""
        # Setup
        sample_workflow_context.stage_results["stage1"] = [
            AgentResult(
                agent_name="triage",
                status=AgentStatus.COMPLETED,
                output={"category": "bug"},
                execution_time=1.0
            )
        ]

        agent = TechnicalTroubleshootAgent()

        # Execute
        should_run = await agent.should_execute(sample_workflow_context)

        # Assert
        assert should_run is True

    @pytest.mark.asyncio
    async def test_should_execute_for_technical_category(self, sample_workflow_context):
        """Test agent executes for Technical category."""
        # Setup
        sample_workflow_context.stage_results["stage1"] = [
            AgentResult(
                agent_name="triage",
                status=AgentStatus.COMPLETED,
                output={"category": "Technical"},
                execution_time=1.0
            )
        ]

        agent = TechnicalTroubleshootAgent()

        # Execute
        should_run = await agent.should_execute(sample_workflow_context)

        # Assert
        assert should_run is True

    @pytest.mark.asyncio
    async def test_should_not_execute_for_inquiry(self, sample_workflow_context):
        """Test agent does NOT execute for non-technical categories."""
        # Setup
        sample_workflow_context.stage_results["stage1"] = [
            AgentResult(
                agent_name="triage",
                status=AgentStatus.COMPLETED,
                output={"category": "inquiry"},
                execution_time=1.0
            )
        ]

        agent = TechnicalTroubleshootAgent()

        # Execute
        should_run = await agent.should_execute(sample_workflow_context)

        # Assert
        assert should_run is False

    @pytest.mark.asyncio
    async def test_should_not_execute_no_triage_result(self, sample_workflow_context):
        """Test agent does NOT execute when triage result missing."""
        # Setup - empty stage1 results
        sample_workflow_context.stage_results["stage1"] = []

        agent = TechnicalTroubleshootAgent()

        # Execute
        should_run = await agent.should_execute(sample_workflow_context)

        # Assert
        assert should_run is False

    @pytest.mark.asyncio
    async def test_process_integration(self, sample_workflow_context):
        """Test full process integration (mocked)."""
        agent = TechnicalTroubleshootAgent()

        # Mock the workflow process
        mock_result = {
            "ticket_id": "SUPPORT-123",
            "bug_analysis": {"is_valid_bug": True, "confidence": 0.85},
            "workarounds": [],
            "jira_bug_ticket": {"created": True}
        }

        with patch.object(agent.workflow, 'process', new_callable=AsyncMock) as mock_process:
            mock_process.return_value = mock_result

            # Execute
            result = await agent.process(sample_workflow_context)

            # Assert
            assert result == mock_result
            mock_process.assert_called_once_with(sample_workflow_context)


# ============================================================================
# Test Workflow Integration
# ============================================================================

class TestTroubleshootingWorkflow:
    """Test workflow integration and state transitions."""

    def test_workflow_conditional_edge_create_ticket(self):
        """Test conditional edge when should create ticket."""
        # Setup
        mock_llm = MagicMock()
        mock_claude = MagicMock()
        mock_jira = MagicMock()

        workflow = TroubleshootingWorkflow(mock_llm, mock_claude, mock_jira)

        state = {
            "bug_validation": {"should_create": True}
        }

        # Execute
        result = workflow._should_create_ticket(state)

        # Assert
        assert result == "create_ticket"

    def test_workflow_conditional_edge_skip_ticket(self):
        """Test conditional edge when should NOT create ticket."""
        # Setup
        mock_llm = MagicMock()
        mock_claude = MagicMock()
        mock_jira = MagicMock()

        workflow = TroubleshootingWorkflow(mock_llm, mock_claude, mock_jira)

        state = {
            "bug_validation": {"should_create": False}
        }

        # Execute
        result = workflow._should_create_ticket(state)

        # Assert
        assert result == "skip_ticket"

    @pytest.mark.asyncio
    async def test_workflow_process_fetches_jira_issue(self, sample_workflow_context):
        """Test workflow fetches Jira issue at start."""
        # Setup
        mock_llm = MagicMock()
        mock_claude = MagicMock()
        mock_jira = MagicMock()
        mock_jira.get_issue.return_value = {
            "key": "SUPPORT-123",
            "summary": "Test issue",
            "description": "Test description",
            "components": [],
            "labels": []
        }

        workflow = TroubleshootingWorkflow(mock_llm, mock_claude, mock_jira)

        # Mock the workflow graph
        with patch.object(workflow.workflow, 'ainvoke', new_callable=AsyncMock) as mock_ainvoke:
            mock_ainvoke.return_value = {
                "final_output": {"ticket_id": "SUPPORT-123"},
                "errors": []
            }

            # Execute
            result = await workflow.process(sample_workflow_context)

            # Assert
            mock_jira.get_issue.assert_called_once_with("SUPPORT-123")
            assert result is not None


# ============================================================================
# Test Error Handling
# ============================================================================

class TestErrorHandling:
    """Test error handling and edge cases."""

    @pytest.mark.asyncio
    async def test_handle_jira_search_failure_gracefully(self, workflow_nodes, initial_state):
        """Test graceful handling of Jira search failures."""
        # Setup
        initial_state["technical_context"] = {"error_codes": ["TEST_ERROR"]}
        initial_state["code_analysis"] = {}
        workflow_nodes.jira_client.search_issues.side_effect = Exception("Jira API down")

        # Execute - should not raise exception
        state = await workflow_nodes.check_workarounds_node(initial_state)

        # Assert
        assert "workarounds" in state
        # Should have empty list since search failed
        similar_workarounds = [w for w in state["workarounds"] if w["type"] == "similar_ticket"]
        assert len(similar_workarounds) == 0

    def test_handle_missing_fields_in_code_analysis(self, workflow_nodes, initial_state):
        """Test handling of incomplete code analysis results."""
        # Setup - missing fields
        initial_state["code_analysis"] = {
            "is_valid_bug": True
            # Missing confidence, root_cause, etc.
        }

        # Execute - should not crash
        state = workflow_nodes.validate_bug_node(initial_state)

        # Assert
        assert "bug_validation" in state
        assert state["bug_validation"]["confidence"] == 0.0  # Default

    def test_handle_empty_ticket_input(self, workflow_nodes):
        """Test handling of empty ticket input."""
        # Setup
        empty_state = {
            "ticket_input": {
                "ticket_id": "TEST-001",
                "title": "",
                "description": "",
                "components": [],
                "labels": []
            },
            "context": None,
            "technical_context": {},
            "code_analysis": {},
            "workarounds": [],
            "bug_validation": {},
            "jira_ticket": None,
            "final_output": None,
            "errors": [],
            "metadata": {}
        }

        # Execute
        state = workflow_nodes.extract_technical_details_node(empty_state)

        # Assert - should handle gracefully
        assert state["technical_context"]["error_codes"] == []
        assert state["technical_context"]["stack_trace"] is None


# ============================================================================
# Test Helper Methods
# ============================================================================

class TestHelperMethods:
    """Test helper methods."""

    def test_get_validation_reason_not_bug(self, workflow_nodes):
        """Test validation reason when not a bug."""
        reason = workflow_nodes._get_validation_reason(
            is_valid_bug=False,
            confidence=0.9,
            should_create=False
        )

        assert "Not identified as a valid bug" in reason

    def test_get_validation_reason_low_confidence(self, workflow_nodes):
        """Test validation reason for low confidence."""
        reason = workflow_nodes._get_validation_reason(
            is_valid_bug=True,
            confidence=0.6,
            should_create=False
        )

        assert "below threshold" in reason
        assert "0.6" in reason

    def test_get_validation_reason_high_confidence(self, workflow_nodes):
        """Test validation reason for high confidence."""
        reason = workflow_nodes._get_validation_reason(
            is_valid_bug=True,
            confidence=0.9,
            should_create=True
        )

        assert "High confidence" in reason
        assert "0.9" in reason

    def test_map_severity_to_priority(self, workflow_nodes, sample_workflow_context):
        """Test severity to priority mapping."""
        # Test critical
        priority = workflow_nodes._map_severity_to_priority("critical", sample_workflow_context)
        assert priority == "Highest"

        # Test high
        priority = workflow_nodes._map_severity_to_priority("high", sample_workflow_context)
        assert priority == "High"

        # Test medium
        priority = workflow_nodes._map_severity_to_priority("medium", sample_workflow_context)
        assert priority == "Medium"

        # Test low
        priority = workflow_nodes._map_severity_to_priority("low", sample_workflow_context)
        assert priority == "Low"

        # Test unknown defaults to medium
        priority = workflow_nodes._map_severity_to_priority("unknown", sample_workflow_context)
        assert priority == "Medium"


# ============================================================================
# Performance and Integration Tests
# ============================================================================

class TestPerformanceAndIntegration:
    """Test performance characteristics and integration scenarios."""

    @pytest.mark.asyncio
    async def test_complete_workflow_happy_path(
        self,
        workflow_nodes,
        initial_state,
        sample_code_analysis_result,
        sample_workflow_context
    ):
        """Test complete workflow execution (happy path)."""
        import time

        # Setup all nodes
        workflow_nodes.claude_code_client.analyze_issue.return_value = sample_code_analysis_result
        workflow_nodes.jira_client.create_issue.return_value = {"key": "BUG-999"}

        with patch('config.settings.Config.AUTO_CREATE_BUG_TICKETS', True):
            with patch('config.settings.Config.BUG_CONFIDENCE_THRESHOLD', 0.75):
                with patch('config.settings.Config.JIRA_URL', 'https://test.atlassian.net'):
                    # Execute all nodes in sequence
                    start = time.time()

                    state = workflow_nodes.extract_technical_details_node(initial_state)
                    state = await workflow_nodes.invoke_claude_code_node(state)
                    state = await workflow_nodes.check_workarounds_node(state)
                    state = workflow_nodes.validate_bug_node(state)

                    # Conditional: should create ticket
                    if state["bug_validation"]["should_create"]:
                        state = await workflow_nodes.create_jira_ticket_node(state)

                    state = workflow_nodes.format_output_node(state)

                    duration = time.time() - start

        # Assert
        assert state["final_output"] is not None
        assert state["final_output"]["bug_analysis"]["is_valid_bug"] is True
        assert state["final_output"]["jira_bug_ticket"]["created"] is True
        assert duration < 5.0  # Should complete quickly with mocks

    @pytest.mark.asyncio
    async def test_workflow_with_all_nodes_skipped(self, workflow_nodes, initial_state):
        """Test workflow when bug is not valid (ticket creation skipped)."""
        # Setup - not a valid bug
        workflow_nodes.claude_code_client.analyze_issue.return_value = CodeAnalysisResult(
            is_valid_bug=False,
            confidence=0.5,
            root_cause="User error, not a bug",
            affected_files=[],
            workaround=None,
            fix_recommendation="User training needed",
            estimated_effort_hours=0,
            analysis_method="claude_code"
        )

        with patch('config.settings.Config.AUTO_CREATE_BUG_TICKETS', True):
            # Execute all nodes
            state = workflow_nodes.extract_technical_details_node(initial_state)
            state = await workflow_nodes.invoke_claude_code_node(state)
            state = await workflow_nodes.check_workarounds_node(state)
            state = workflow_nodes.validate_bug_node(state)

            # Should NOT create ticket
            assert state["bug_validation"]["should_create"] is False

            state = workflow_nodes.format_output_node(state)

        # Assert
        assert state["final_output"]["bug_analysis"]["is_valid_bug"] is False
        assert state["final_output"]["jira_bug_ticket"]["created"] is False
