"""
PostgreSQL Analytics Client for workflow and agent execution tracking.

This client provides methods for inserting and querying analytics data
with connection pooling and graceful error handling.
"""
import logging
import json
from typing import Dict, Any, Optional, List
from datetime import datetime
from psycopg2.pool import <PERSON><PERSON>on<PERSON><PERSON><PERSON>ool
from psycopg2.extras import RealDictCursor
import psycopg2

from config.settings import Config
from app.constants.errors import DatabaseErrors
from app.exceptions.database import ConnectionPoolError, ConnectionPoolInitError

logger = logging.getLogger(__name__)


class PostgresAnalyticsClient:
    """
    Client for PostgreSQL analytics database operations.

    Provides methods for:
    - Inserting workflow execution records
    - Updating workflow execution metrics
    - Inserting agent execution records
    - Querying workflow statistics

    Features:
    - Connection pooling for performance
    - Graceful error handling (never crashes workflows)
    - Automatic JSON serialization for JSONB fields
    """

    def __init__(self, min_conn: int = 1, max_conn: int = 10):
        """
        Initialize the analytics client with connection pooling.

        Args:
            min_conn: Minimum number of connections in pool
            max_conn: Maximum number of connections in pool
        """
        self.pool = None
        try:
            self.pool = SimpleConnectionPool(
                min_conn,
                max_conn,
                host=Config.POSTGRESQL_HOST,
                port=Config.POSTGRESQL_PORT,
                user=Config.POSTGRESQL_USER,
                password=Config.POSTGRESQL_PASSWORD,
                database=Config.POSTGRESQL_DATABASE
            )
            logger.info("PostgresAnalyticsClient initialized with connection pool")
        except Exception as e:
            logger.error(f"{DatabaseErrors.POOL_INIT_FAILED}: {e}")
            # Don't raise - graceful degradation

    def _get_connection(self):
        """Get connection from pool."""
        if self.pool is None:
            return None
        try:
            return self.pool.getconn()
        except Exception as e:
            logger.error(f"{DatabaseErrors.POOL_GET_CONN_FAILED}: {e}")
            return None

    def _return_connection(self, conn):
        """Return connection to pool."""
        if self.pool and conn:
            try:
                self.pool.putconn(conn)
            except Exception as e:
                logger.error(f"{DatabaseErrors.POOL_RETURN_CONN_FAILED}: {e}")

    def insert_workflow_execution(
        self,
        correlation_id: str,
        ticket_id: str,
        workflow_start_time: datetime,
        workflow_status: str,
        stages_total: int = 4,
        webhook_payload: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Insert a new workflow execution record at workflow start.

        Args:
            correlation_id: Unique identifier for distributed tracing
            ticket_id: Jira ticket ID
            workflow_start_time: Workflow start timestamp
            workflow_status: Status ('running', 'completed', 'failed', 'partial')
            stages_total: Total number of stages (default: 4)
            webhook_payload: Original webhook JSON data

        Returns:
            True if successful, False otherwise
        """
        conn = self._get_connection()
        if conn is None:
            logger.error(DatabaseErrors.NO_CONNECTION)
            return False

        try:
            with conn.cursor() as cursor:
                sql = """
                INSERT INTO workflow_executions (
                    correlation_id,
                    ticket_id,
                    workflow_start_time,
                    workflow_status,
                    stages_total,
                    webhook_payload
                ) VALUES (%s, %s, %s, %s, %s, %s)
                """

                # Serialize webhook_payload to JSON
                webhook_json = json.dumps(webhook_payload) if webhook_payload else None

                cursor.execute(
                    sql,
                    (
                        correlation_id,
                        ticket_id,
                        workflow_start_time,
                        workflow_status,
                        stages_total,
                        webhook_json
                    )
                )
                conn.commit()
                logger.info(f"Inserted workflow execution: {correlation_id}")
                return True

        except Exception as e:
            logger.error(f"Failed to insert workflow execution {correlation_id}: {e}")
            if conn:
                conn.rollback()
            return False

        finally:
            self._return_connection(conn)

    def update_workflow_execution(
        self,
        correlation_id: str,
        workflow_end_time: Optional[datetime] = None,
        total_execution_time_ms: Optional[int] = None,
        workflow_status: Optional[str] = None,
        error_message: Optional[str] = None,
        stages_completed: Optional[int] = None,
        agents_executed: Optional[int] = None,
        agents_completed: Optional[int] = None,
        agents_failed: Optional[int] = None,
        agents_skipped: Optional[int] = None
    ) -> bool:
        """
        Update workflow execution with final metrics.

        Args:
            correlation_id: Workflow correlation ID
            workflow_end_time: Workflow completion timestamp
            total_execution_time_ms: Total execution time in milliseconds
            workflow_status: Final status
            error_message: Error message if failed
            stages_completed: Number of completed stages
            agents_executed: Total agents executed
            agents_completed: Number of completed agents
            agents_failed: Number of failed agents
            agents_skipped: Number of skipped agents

        Returns:
            True if successful, False otherwise
        """
        conn = self._get_connection()
        if conn is None:
            logger.error(DatabaseErrors.NO_CONNECTION)
            return False

        try:
            # Build dynamic UPDATE query based on provided fields
            update_fields = []
            params = []

            if workflow_end_time is not None:
                update_fields.append("workflow_end_time = %s")
                params.append(workflow_end_time)

            if total_execution_time_ms is not None:
                update_fields.append("total_execution_time_ms = %s")
                params.append(total_execution_time_ms)

            if workflow_status is not None:
                update_fields.append("workflow_status = %s")
                params.append(workflow_status)

            if error_message is not None:
                update_fields.append("error_message = %s")
                params.append(error_message)

            if stages_completed is not None:
                update_fields.append("stages_completed = %s")
                params.append(stages_completed)

            if agents_executed is not None:
                update_fields.append("agents_executed = %s")
                params.append(agents_executed)

            if agents_completed is not None:
                update_fields.append("agents_completed = %s")
                params.append(agents_completed)

            if agents_failed is not None:
                update_fields.append("agents_failed = %s")
                params.append(agents_failed)

            if agents_skipped is not None:
                update_fields.append("agents_skipped = %s")
                params.append(agents_skipped)

            if not update_fields:
                logger.warning(f"No fields to update for workflow {correlation_id}")
                return False

            params.append(correlation_id)
            sql = f"""
            UPDATE workflow_executions
            SET {', '.join(update_fields)}
            WHERE correlation_id = %s
            """

            with conn.cursor() as cursor:
                cursor.execute(sql, params)
                conn.commit()
                logger.info(f"Updated workflow execution: {correlation_id}")
                return True

        except Exception as e:
            logger.error(f"Failed to update workflow execution {correlation_id}: {e}")
            if conn:
                conn.rollback()
            return False

        finally:
            self._return_connection(conn)

    def insert_agent_execution(
        self,
        correlation_id: str,
        ticket_id: str,
        agent_name: str,
        stage_name: str,
        start_time: datetime,
        agent_status: str,
        end_time: Optional[datetime] = None,
        execution_time_ms: Optional[int] = None,
        execution_order: Optional[int] = None,
        should_execute_result: Optional[bool] = None,
        error_message: Optional[str] = None,
        output_data: Optional[Dict[str, Any]] = None,
        output_summary: Optional[str] = None
    ) -> bool:
        """
        Insert agent execution record after agent completes.

        Args:
            correlation_id: Workflow correlation ID
            ticket_id: Jira ticket ID
            agent_name: Name of the agent
            stage_name: Stage identifier
            start_time: Agent start timestamp
            agent_status: Status ('completed', 'failed', 'skipped')
            end_time: Agent completion timestamp
            execution_time_ms: Execution time in milliseconds
            execution_order: Order within workflow
            should_execute_result: Was agent conditionally executed
            error_message: Error message if failed
            output_data: Agent-specific output (JSONB)
            output_summary: Human-readable summary

        Returns:
            True if successful, False otherwise
        """
        conn = self._get_connection()
        if conn is None:
            logger.error(DatabaseErrors.NO_CONNECTION)
            return False

        try:
            with conn.cursor() as cursor:
                sql = """
                INSERT INTO agent_executions (
                    correlation_id,
                    ticket_id,
                    agent_name,
                    stage_name,
                    execution_order,
                    start_time,
                    end_time,
                    execution_time_ms,
                    agent_status,
                    should_execute_result,
                    error_message,
                    output_data,
                    output_summary
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """

                # Serialize output_data to JSON
                output_json = json.dumps(output_data) if output_data else None

                cursor.execute(
                    sql,
                    (
                        correlation_id,
                        ticket_id,
                        agent_name,
                        stage_name,
                        execution_order,
                        start_time,
                        end_time,
                        execution_time_ms,
                        agent_status,
                        should_execute_result,
                        error_message,
                        output_json,
                        output_summary
                    )
                )
                conn.commit()
                logger.info(f"Inserted agent execution: {agent_name} ({correlation_id})")
                return True

        except Exception as e:
            logger.error(
                f"Failed to insert agent execution {agent_name} ({correlation_id}): {e}"
            )
            if conn:
                conn.rollback()
            return False

        finally:
            self._return_connection(conn)

    def get_workflow_stats(
        self,
        correlation_id: Optional[str] = None,
        ticket_id: Optional[str] = None
    ) -> Optional[Dict[str, Any]] | List[Dict[str, Any]]:
        """
        Query workflow execution statistics.

        Args:
            correlation_id: Query by correlation ID (returns single result)
            ticket_id: Query by ticket ID (returns list of results)

        Returns:
            Single dict if correlation_id provided, list of dicts if ticket_id provided,
            None if not found or on error
        """
        conn = self._get_connection()
        if conn is None:
            logger.error(DatabaseErrors.NO_CONNECTION)
            return None

        try:
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                if correlation_id:
                    # Query by correlation_id - return single result
                    sql = """
                    SELECT * FROM workflow_executions
                    WHERE correlation_id = %s
                    """
                    cursor.execute(sql, (correlation_id,))
                    result = cursor.fetchone()
                    return dict(result) if result else None

                elif ticket_id:
                    # Query by ticket_id - return list
                    sql = """
                    SELECT * FROM workflow_executions
                    WHERE ticket_id = %s
                    ORDER BY workflow_start_time DESC
                    """
                    cursor.execute(sql, (ticket_id,))
                    results = cursor.fetchall()
                    return [dict(row) for row in results]

                else:
                    logger.warning("get_workflow_stats called without correlation_id or ticket_id")
                    return None

        except Exception as e:
            logger.error(f"Failed to query workflow stats: {e}")
            return None

        finally:
            self._return_connection(conn)

    def close(self):
        """Close all connections in the pool."""
        if self.pool:
            try:
                self.pool.closeall()
                logger.info("Closed all connections in analytics client pool")
            except Exception as e:
                logger.error(f"{DatabaseErrors.POOL_CLOSE_FAILED}: {e}")


# Create singleton instance
postgres_analytics_client = PostgresAnalyticsClient()
