"""
Error message constants for the agentic AI platform.

Centralizes error messages for consistency and easier maintenance.
"""


class DatabaseErrors:
    """Database-related error messages."""
    NO_CONNECTION = "No database connection available"
    CONNECTION_FAILED = "Failed to establish database connection"
    QUERY_FAILED = "Database query failed"
    POOL_INIT_FAILED = "Failed to initialize database connection pool"
    POOL_GET_CONN_FAILED = "Failed to get connection from pool"
    POOL_RETURN_CONN_FAILED = "Failed to return connection to pool"
    POOL_CLOSE_FAILED = "Failed to close connection pool"


class WorkflowErrors:
    """Workflow-related error messages."""
    INVALID_STAGE = "Invalid workflow stage"
    AGENT_FAILED = "Agent execution failed"
    AGENT_NOT_FOUND = "Agent not found in registry"
    EXECUTION_FAILED = "Workflow execution failed"
    INVALID_CONTEXT = "Invalid workflow context"
