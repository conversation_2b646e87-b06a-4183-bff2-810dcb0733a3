"""Application configuration management."""
import os
from typing import Optional
from dotenv import load_dotenv

load_dotenv()


class Config:
    """Main configuration class."""
    
    # Application
    FLASK_ENV: str = os.getenv('FLASK_ENV', 'development')
    FLASK_DEBUG: bool = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    MAX_WORKERS: int = int(os.getenv('MAX_WORKERS', '10'))
    LOG_LEVEL: str = os.getenv('LOG_LEVEL', 'INFO')
    LOG_DIR: str = os.getenv('LOG_DIR', '/var/log/jira-events')
    
    # PostgreSQL
    POSTGRESQL_HOST: str = os.getenv('POSTGRESQL_HOST', 'localhost')
    POSTGRESQL_PORT: int = int(os.getenv('POSTGRESQL_PORT', '5432'))
    POSTGRESQL_USER: str = os.getenv('POSTGRESQL_USER', 'postgres')
    POSTGRESQL_PASSWORD: str = os.getenv('POSTGRESQL_PASSWORD', '')
    POSTGRESQL_DATABASE: str = os.getenv('POSTGRESQL_DATABASE', 'analytics')
    
    # Jira
    JIRA_URL: str = os.getenv('JIRA_URL', '')
    JIRA_EMAIL: str = os.getenv('JIRA_EMAIL', '')
    JIRA_API_TOKEN: str = os.getenv('JIRA_API_TOKEN', '')

    # LLM Configuration
    ANTHROPIC_API_KEY: str = os.getenv('ANTHROPIC_API_KEY', '')
    LLM_MODEL: str = os.getenv('LLM_MODEL', 'claude-3-5-sonnet-20241022')
    
    # Email (Mandrill)
    MANDRILL_API_KEY: str = os.getenv('MANDRILL_API_KEY', '')
    MANDRILL_BASE_URL: str = os.getenv('MANDRILL_BASE_URL', '')
    MANDRILL_FROM_EMAIL: str = os.getenv('MANDRILL_FROM_EMAIL', '')
    MANDRILL_FROM_NAME: str = os.getenv('MANDRILL_FROM_NAME', '')
    
    # Agent Thresholds
    TRIAGE_CONFIDENCE_THRESHOLD: float = float(os.getenv('TRIAGE_CONFIDENCE_THRESHOLD', '0.7'))
    SENTIMENT_HIGH_RISK_THRESHOLD: float = float(os.getenv('SENTIMENT_HIGH_RISK_THRESHOLD', '-0.5'))
    ESCALATION_PROBABILITY_THRESHOLD: float = float(os.getenv('ESCALATION_PROBABILITY_THRESHOLD', '0.7'))
    AUTO_RESPONSE_CONFIDENCE_THRESHOLD: float = float(os.getenv('AUTO_RESPONSE_CONFIDENCE_THRESHOLD', '0.8'))
    QUALITY_CHECK_MIN_SCORE: float = float(os.getenv('QUALITY_CHECK_MIN_SCORE', '0.6'))
    
    # Email Recipients
    SUPPORT_LEAD_EMAIL: str = os.getenv('SUPPORT_LEAD_EMAIL', '')
    ENGINEERING_LEAD_EMAIL: str = os.getenv('ENGINEERING_LEAD_EMAIL', '')
    PRODUCT_MANAGER_EMAIL: str = os.getenv('PRODUCT_MANAGER_EMAIL', '')
    CUSTOMER_SUCCESS_EMAIL: str = os.getenv('CUSTOMER_SUCCESS_EMAIL', '')

    # Code Analysis Configuration (Technical Troubleshooting Agent)
    CODE_REPO_PATH: str = os.getenv('CODE_REPO_PATH', '/path/to/repo')
    CLAUDE_CODE_TIMEOUT: int = int(os.getenv('CLAUDE_CODE_TIMEOUT', '120'))
    CLAUDE_CODE_ENABLED: bool = os.getenv('CLAUDE_CODE_ENABLED', 'True').lower() == 'true'
    BUG_CONFIDENCE_THRESHOLD: float = float(os.getenv('BUG_CONFIDENCE_THRESHOLD', '0.75'))
    AUTO_CREATE_BUG_TICKETS: bool = os.getenv('AUTO_CREATE_BUG_TICKETS', 'True').lower() == 'true'
    
    @classmethod
    def validate(cls) -> bool:
        """Validate that required configuration is present."""
        required = ['JIRA_URL', 'JIRA_EMAIL', 'JIRA_API_TOKEN']
        missing = [key for key in required if not getattr(cls, key)]
        if missing:
            raise ValueError(f"Missing required configuration: {', '.join(missing)}")
        return True