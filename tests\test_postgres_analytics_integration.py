"""Integration tests for PostgresAnalyticsClient with real database."""
import pytest
import os
from datetime import datetime
from dotenv import load_dotenv

from app.models.integrations.postgres_analytics_client import PostgresAnalyticsClient

# Load environment variables
load_dotenv()

# Test configuration
POSTGRES_HOST = os.getenv('POSTGRESQL_HOST', 'localhost')
POSTGRES_PORT = os.getenv('POSTGRESQL_PORT', '5432')
POSTGRES_USER = os.getenv('POSTGRESQL_USER', 'postgres')
POSTGRES_PASSWORD = os.getenv('POSTGRESQL_PASSWORD', 'password')
POSTGRES_DATABASE = os.getenv('POSTGRESQL_DATABASE', 'ai_agents_analytics')


@pytest.fixture(scope='module')
def analytics_client():
    """Create a real PostgresAnalyticsClient instance."""
    if not all([POSTGRES_HOST, POSTGRES_USER, POSTGRES_PASSWORD, POSTGRES_DATABASE]):
        pytest.skip("PostgreSQL credentials not configured in .env file")

    client = PostgresAnalyticsClient()
    if client.pool is None:
        pytest.skip("Failed to initialize analytics client connection pool")

    yield client

    # Cleanup
    client.close()


@pytest.fixture(scope='function')
def test_correlation_id():
    """Generate unique correlation ID for each test."""
    timestamp = int(datetime.now().timestamp() * 1000)
    return f"TEST-INTEG-{timestamp}"


class TestWorkflowExecutionLifecycle:
    """Test complete workflow execution lifecycle."""

    def test_insert_and_query_workflow(self, analytics_client, test_correlation_id):
        """Test inserting a workflow and querying it back."""
        # Insert workflow
        workflow_data = {
            'correlation_id': test_correlation_id,
            'ticket_id': 'TEST-123',
            'workflow_start_time': datetime.now(),
            'workflow_status': 'running',
            'stages_total': 4,
            'webhook_payload': {'test': 'data', 'ticket': 'TEST-123'}
        }

        result = analytics_client.insert_workflow_execution(**workflow_data)
        assert result is True

        # Query it back
        workflow = analytics_client.get_workflow_stats(correlation_id=test_correlation_id)
        assert workflow is not None
        assert workflow['correlation_id'] == test_correlation_id
        assert workflow['ticket_id'] == 'TEST-123'
        assert workflow['workflow_status'] == 'running'

    def test_update_workflow_execution(self, analytics_client, test_correlation_id):
        """Test updating a workflow with final status."""
        # Insert workflow
        workflow_data = {
            'correlation_id': test_correlation_id,
            'ticket_id': 'TEST-456',
            'workflow_start_time': datetime.now(),
            'workflow_status': 'running'
        }

        result = analytics_client.insert_workflow_execution(**workflow_data)
        assert result is True

        # Update with completion data
        update_data = {
            'correlation_id': test_correlation_id,
            'workflow_end_time': datetime.now(),
            'total_execution_time_ms': 5000,
            'workflow_status': 'completed',
            'stages_completed': 4,
            'agents_executed': 10,
            'agents_completed': 9,
            'agents_failed': 0,
            'agents_skipped': 1
        }

        result = analytics_client.update_workflow_execution(**update_data)
        assert result is True

        # Verify update
        workflow = analytics_client.get_workflow_stats(correlation_id=test_correlation_id)
        assert workflow['workflow_status'] == 'completed'
        assert workflow['total_execution_time_ms'] == 5000
        assert workflow['agents_executed'] == 10
        assert workflow['agents_completed'] == 9

    def test_update_workflow_with_error(self, analytics_client, test_correlation_id):
        """Test updating workflow with error status."""
        # Insert workflow
        workflow_data = {
            'correlation_id': test_correlation_id,
            'ticket_id': 'TEST-789',
            'workflow_start_time': datetime.now(),
            'workflow_status': 'running'
        }

        analytics_client.insert_workflow_execution(**workflow_data)

        # Update with failure
        update_data = {
            'correlation_id': test_correlation_id,
            'workflow_end_time': datetime.now(),
            'workflow_status': 'failed',
            'error_message': 'Integration test error'
        }

        result = analytics_client.update_workflow_execution(**update_data)
        assert result is True

        # Verify error recorded
        workflow = analytics_client.get_workflow_stats(correlation_id=test_correlation_id)
        assert workflow['workflow_status'] == 'failed'
        assert workflow['error_message'] == 'Integration test error'


class TestAgentExecutionLifecycle:
    """Test agent execution recording."""

    def test_insert_agent_execution(self, analytics_client, test_correlation_id):
        """Test inserting agent execution record."""
        # First insert workflow (foreign key requirement)
        workflow_data = {
            'correlation_id': test_correlation_id,
            'ticket_id': 'TEST-AGENT-1',
            'workflow_start_time': datetime.now(),
            'workflow_status': 'running'
        }
        analytics_client.insert_workflow_execution(**workflow_data)

        # Insert agent execution
        agent_data = {
            'correlation_id': test_correlation_id,
            'ticket_id': 'TEST-AGENT-1',
            'agent_name': 'sentiment',
            'stage_name': 'stage1',
            'execution_order': 1,
            'start_time': datetime.now(),
            'end_time': datetime.now(),
            'execution_time_ms': 150,
            'agent_status': 'completed',
            'should_execute_result': True,
            'output_data': {'sentiment_score': 0.8, 'confidence': 0.9}
        }

        result = analytics_client.insert_agent_execution(**agent_data)
        assert result is True

    def test_insert_multiple_agents_for_workflow(self, analytics_client, test_correlation_id):
        """Test inserting multiple agent executions for same workflow."""
        # Insert workflow
        workflow_data = {
            'correlation_id': test_correlation_id,
            'ticket_id': 'TEST-MULTI-1',
            'workflow_start_time': datetime.now(),
            'workflow_status': 'running'
        }
        analytics_client.insert_workflow_execution(**workflow_data)

        # Insert multiple agents
        agents = [
            {
                'correlation_id': test_correlation_id,
                'ticket_id': 'TEST-MULTI-1',
                'agent_name': 'sentiment',
                'stage_name': 'stage1',
                'execution_order': 1,
                'start_time': datetime.now(),
                'agent_status': 'completed',
                'execution_time_ms': 100
            },
            {
                'correlation_id': test_correlation_id,
                'ticket_id': 'TEST-MULTI-1',
                'agent_name': 'escalation',
                'stage_name': 'stage1',
                'execution_order': 2,
                'start_time': datetime.now(),
                'agent_status': 'completed',
                'execution_time_ms': 200
            },
            {
                'correlation_id': test_correlation_id,
                'ticket_id': 'TEST-MULTI-1',
                'agent_name': 'auto_response',
                'stage_name': 'stage2',
                'execution_order': 3,
                'start_time': datetime.now(),
                'agent_status': 'skipped',
                'should_execute_result': False,
                'execution_time_ms': 0
            }
        ]

        for agent in agents:
            result = analytics_client.insert_agent_execution(**agent)
            assert result is True

    def test_insert_failed_agent_execution(self, analytics_client, test_correlation_id):
        """Test inserting failed agent execution with error."""
        # Insert workflow
        workflow_data = {
            'correlation_id': test_correlation_id,
            'ticket_id': 'TEST-FAIL-1',
            'workflow_start_time': datetime.now(),
            'workflow_status': 'running'
        }
        analytics_client.insert_workflow_execution(**workflow_data)

        # Insert failed agent
        agent_data = {
            'correlation_id': test_correlation_id,
            'ticket_id': 'TEST-FAIL-1',
            'agent_name': 'technical_troubleshoot',
            'stage_name': 'stage2',
            'start_time': datetime.now(),
            'agent_status': 'failed',
            'error_message': 'API timeout after 30s',
            'execution_time_ms': 30000
        }

        result = analytics_client.insert_agent_execution(**agent_data)
        assert result is True


class TestForeignKeyConstraints:
    """Test foreign key relationships work correctly."""

    def test_cannot_insert_agent_without_workflow(self, analytics_client):
        """Test that agent insertion fails without parent workflow."""
        # Try to insert agent with non-existent correlation_id
        agent_data = {
            'correlation_id': 'NONEXISTENT-ID-999999',
            'ticket_id': 'TEST-FK-1',
            'agent_name': 'sentiment',
            'stage_name': 'stage1',
            'start_time': datetime.now(),
            'agent_status': 'completed'
        }

        # Should fail due to foreign key constraint
        result = analytics_client.insert_agent_execution(**agent_data)
        assert result is False

    def test_cascade_delete_agents_with_workflow(self, analytics_client, test_correlation_id):
        """Test that deleting workflow cascades to agent executions."""
        # Insert workflow
        workflow_data = {
            'correlation_id': test_correlation_id,
            'ticket_id': 'TEST-CASCADE-1',
            'workflow_start_time': datetime.now(),
            'workflow_status': 'running'
        }
        analytics_client.insert_workflow_execution(**workflow_data)

        # Insert agent
        agent_data = {
            'correlation_id': test_correlation_id,
            'ticket_id': 'TEST-CASCADE-1',
            'agent_name': 'sentiment',
            'stage_name': 'stage1',
            'start_time': datetime.now(),
            'agent_status': 'completed'
        }
        analytics_client.insert_agent_execution(**agent_data)

        # Manually delete workflow (cascade should delete agents)
        # This tests the database constraint is properly configured
        # Note: In production, we wouldn't delete workflows, but this validates the schema


class TestQueryOperations:
    """Test various query operations."""

    def test_query_workflow_by_ticket_id(self, analytics_client, test_correlation_id):
        """Test querying workflows by ticket ID."""
        # Insert workflow
        workflow_data = {
            'correlation_id': test_correlation_id,
            'ticket_id': 'TEST-QUERY-1',
            'workflow_start_time': datetime.now(),
            'workflow_status': 'completed'
        }
        analytics_client.insert_workflow_execution(**workflow_data)

        # Query by ticket_id
        workflows = analytics_client.get_workflow_stats(ticket_id='TEST-QUERY-1')
        assert isinstance(workflows, list)
        assert len(workflows) >= 1
        assert any(w['correlation_id'] == test_correlation_id for w in workflows)

    def test_query_nonexistent_workflow(self, analytics_client):
        """Test querying workflow that doesn't exist."""
        result = analytics_client.get_workflow_stats(correlation_id='NONEXISTENT-999999')
        assert result is None


class TestConcurrentOperations:
    """Test connection pool handles concurrent operations."""

    def test_multiple_operations_use_pool_efficiently(self, analytics_client):
        """Test multiple sequential operations use connection pool."""
        timestamp = int(datetime.now().timestamp() * 1000)

        # Perform multiple operations
        for i in range(5):
            correlation_id = f"TEST-POOL-{timestamp}-{i}"

            # Insert workflow
            workflow_data = {
                'correlation_id': correlation_id,
                'ticket_id': f'TEST-POOL-{i}',
                'workflow_start_time': datetime.now(),
                'workflow_status': 'running'
            }
            result = analytics_client.insert_workflow_execution(**workflow_data)
            assert result is True

            # Update workflow
            update_data = {
                'correlation_id': correlation_id,
                'workflow_status': 'completed',
                'workflow_end_time': datetime.now()
            }
            result = analytics_client.update_workflow_execution(**update_data)
            assert result is True


class TestDataIntegrity:
    """Test data integrity and constraints."""

    def test_unique_correlation_id_constraint(self, analytics_client, test_correlation_id):
        """Test that duplicate correlation_id is rejected."""
        # Insert workflow
        workflow_data = {
            'correlation_id': test_correlation_id,
            'ticket_id': 'TEST-UNIQUE-1',
            'workflow_start_time': datetime.now(),
            'workflow_status': 'running'
        }
        result = analytics_client.insert_workflow_execution(**workflow_data)
        assert result is True

        # Try to insert duplicate
        result = analytics_client.insert_workflow_execution(**workflow_data)
        assert result is False

    def test_jsonb_field_storage(self, analytics_client, test_correlation_id):
        """Test storing complex JSON data in JSONB fields."""
        # Insert workflow with complex webhook payload
        workflow_data = {
            'correlation_id': test_correlation_id,
            'ticket_id': 'TEST-JSON-1',
            'workflow_start_time': datetime.now(),
            'workflow_status': 'running',
            'webhook_payload': {
                'issue': {
                    'key': 'TEST-123',
                    'fields': {
                        'summary': 'Test issue',
                        'description': 'Test description',
                        'priority': {'name': 'High'}
                    }
                },
                'changelog': {
                    'items': [
                        {'field': 'status', 'fromString': 'Open', 'toString': 'In Progress'}
                    ]
                }
            }
        }

        result = analytics_client.insert_workflow_execution(**workflow_data)
        assert result is True

        # Query back and verify JSONB preserved
        workflow = analytics_client.get_workflow_stats(correlation_id=test_correlation_id)
        assert workflow is not None
        assert 'webhook_payload' in workflow
        assert workflow['webhook_payload']['issue']['key'] == 'TEST-123'


class TestEndToEndWorkflow:
    """Test complete end-to-end workflow scenario."""

    def test_complete_workflow_with_all_agents(self, analytics_client, test_correlation_id):
        """Test complete workflow execution with multiple stages and agents."""
        # 1. Insert workflow at start
        workflow_data = {
            'correlation_id': test_correlation_id,
            'ticket_id': 'TEST-E2E-1',
            'workflow_start_time': datetime.now(),
            'workflow_status': 'running',
            'stages_total': 4,
            'webhook_payload': {'test': 'e2e_workflow'}
        }
        result = analytics_client.insert_workflow_execution(**workflow_data)
        assert result is True

        # 2. Insert Stage 1 agents
        stage1_agents = [
            {'agent_name': 'sentiment', 'execution_time_ms': 100, 'agent_status': 'completed'},
            {'agent_name': 'escalation', 'execution_time_ms': 150, 'agent_status': 'completed'}
        ]

        for idx, agent in enumerate(stage1_agents, start=1):
            agent_data = {
                'correlation_id': test_correlation_id,
                'ticket_id': 'TEST-E2E-1',
                'stage_name': 'stage1',
                'execution_order': idx,
                'start_time': datetime.now(),
                **agent
            }
            result = analytics_client.insert_agent_execution(**agent_data)
            assert result is True

        # 3. Insert Stage 2 agents (one skipped)
        stage2_agents = [
            {'agent_name': 'auto_response', 'execution_time_ms': 0, 'agent_status': 'skipped', 'should_execute_result': False},
            {'agent_name': 'kb_search', 'execution_time_ms': 200, 'agent_status': 'completed'}
        ]

        for idx, agent in enumerate(stage2_agents, start=3):
            agent_data = {
                'correlation_id': test_correlation_id,
                'ticket_id': 'TEST-E2E-1',
                'stage_name': 'stage2',
                'execution_order': idx,
                'start_time': datetime.now(),
                **agent
            }
            result = analytics_client.insert_agent_execution(**agent_data)
            assert result is True

        # 4. Update workflow with final status
        update_data = {
            'correlation_id': test_correlation_id,
            'workflow_end_time': datetime.now(),
            'total_execution_time_ms': 4500,
            'workflow_status': 'completed',
            'stages_completed': 4,
            'agents_executed': 4,
            'agents_completed': 3,
            'agents_failed': 0,
            'agents_skipped': 1
        }
        result = analytics_client.update_workflow_execution(**update_data)
        assert result is True

        # 5. Verify final state
        workflow = analytics_client.get_workflow_stats(correlation_id=test_correlation_id)
        assert workflow['workflow_status'] == 'completed'
        assert workflow['agents_executed'] == 4
        assert workflow['agents_completed'] == 3
        assert workflow['agents_skipped'] == 1
        assert workflow['total_execution_time_ms'] == 4500
