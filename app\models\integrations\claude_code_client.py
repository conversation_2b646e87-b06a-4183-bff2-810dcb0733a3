"""
Claude Code Client - Subprocess wrapper for code analysis.

This client invokes Claude Code CLI as a subprocess to perform deep codebase
analysis for the Technical Troubleshooting Agent.
"""
import asyncio
import json
import logging
import subprocess
from dataclasses import dataclass
from typing import Dict, Any, List, Optional

from config.settings import Config

logger = logging.getLogger(__name__)


class ClaudeCodeError(Exception):
    """Base exception for Claude Code client errors."""
    pass


class ClaudeCodeTimeoutError(ClaudeCodeError):
    """Raised when Claude Code execution exceeds timeout."""
    pass


class ClaudeCodeSubprocessError(ClaudeCodeError):
    """Raised when Claude Code subprocess fails."""
    pass


@dataclass
class CodeAnalysisResult:
    """Result from Claude Code analysis."""
    is_valid_bug: bool
    confidence: float
    root_cause: str
    affected_files: List[Dict[str, Any]]
    workaround: Optional[str]
    fix_recommendation: str
    estimated_effort_hours: int
    analysis_method: str  # "claude_code" or "llm_fallback"
    raw_response: Optional[str] = None
    error: Optional[str] = None


class ClaudeCodeClient:
    """
    Wrapper for Claude Code CLI subprocess invocation.

    Provides async interface for invoking Claude Code to analyze technical
    issues with timeout handling and LLM fallback.
    """

    def __init__(
        self,
        repo_path: Optional[str] = None,
        timeout: Optional[int] = None,
        enabled: Optional[bool] = None
    ):
        """
        Initialize Claude Code client.

        Args:
            repo_path: Path to codebase (defaults to Config.CODE_REPO_PATH)
            timeout: Max execution time in seconds (defaults to Config.CLAUDE_CODE_TIMEOUT)
            enabled: Whether Claude Code is enabled (defaults to Config.CLAUDE_CODE_ENABLED)
        """
        self.repo_path = repo_path or Config.CODE_REPO_PATH
        self.timeout = timeout or Config.CLAUDE_CODE_TIMEOUT
        self.enabled = enabled if enabled is not None else Config.CLAUDE_CODE_ENABLED
        self.logger = logging.getLogger(__name__)

    async def analyze_issue(
        self,
        issue_description: str,
        error_details: Dict[str, Any],
        repo_path: Optional[str] = None
    ) -> CodeAnalysisResult:
        """
        Invoke Claude Code to analyze a technical issue.

        Args:
            issue_description: Summary of the issue
            error_details: Dict with error_code, stack_trace, components
            repo_path: Override default repo path

        Returns:
            CodeAnalysisResult with root cause, affected files, confidence

        Raises:
            ClaudeCodeTimeoutError: If execution exceeds timeout
            ClaudeCodeError: If subprocess fails
        """
        if not self.enabled:
            self.logger.info("Claude Code is disabled, using LLM fallback")
            return await self._llm_only_analysis(issue_description, error_details)

        analysis_repo_path = repo_path or self.repo_path

        try:
            # Build focused prompt
            prompt = self._build_prompt(issue_description, error_details, analysis_repo_path)

            # Execute with timeout
            result = await asyncio.wait_for(
                self._execute_claude_code(prompt, analysis_repo_path),
                timeout=self.timeout
            )

            # Parse response
            return self._parse_response(result, "claude_code")

        except asyncio.TimeoutError:
            self.logger.warning(
                f"Claude Code timeout after {self.timeout}s, falling back to LLM"
            )
            return await self._llm_only_analysis(issue_description, error_details)

        except subprocess.CalledProcessError as e:
            self.logger.error(f"Claude Code subprocess failed: {e.stderr}")
            return await self._llm_only_analysis(issue_description, error_details)

        except Exception as e:
            self.logger.error(f"Unexpected error in Claude Code execution: {str(e)}", exc_info=True)
            return await self._llm_only_analysis(issue_description, error_details)

    def _build_prompt(
        self,
        issue_description: str,
        error_details: Dict[str, Any],
        repo_path: str
    ) -> str:
        """
        Build a focused prompt for Claude Code analysis.

        Args:
            issue_description: Issue summary
            error_details: Technical details (error codes, stack traces, etc.)
            repo_path: Repository path

        Returns:
            Structured prompt string
        """
        error_code = error_details.get('error_code', 'N/A')
        stack_trace = error_details.get('stack_trace', 'N/A')
        component = error_details.get('component', 'Unknown')

        prompt = f"""
ROLE: You are analyzing a production issue in our codebase.

ISSUE DETAILS:
- Summary: {issue_description}
- Error Code: {error_code}
- Component: {component}
- Stack Trace: {stack_trace}

ANALYSIS REQUIRED:
1. Is this a valid bug in our code? (yes/no with confidence 0.0-1.0)
2. Root cause analysis (be specific and detailed)
3. Affected files with exact line numbers
4. Quick workaround (if available)
5. Proper fix recommendation
6. Estimated effort in hours

CONSTRAINTS:
- Search in: {repo_path}
- Focus on: backend services, API endpoints, database layer, authentication, configuration
- Priority: Accuracy over speed
- Provide specific file paths and line numbers

OUTPUT FORMAT: Return ONLY valid JSON (no markdown, no code fences) in this exact structure:
{{
  "is_valid_bug": true,
  "confidence": 0.87,
  "root_cause": "Detailed explanation of the root cause",
  "affected_files": [
    {{"file": "path/to/file.py", "line": 89, "issue": "Description of issue at this location"}},
    {{"file": "path/to/config.yaml", "line": 12, "issue": "Configuration problem"}}
  ],
  "workaround": "Step-by-step temporary fix (or null if none)",
  "fix_recommendation": "Detailed proper fix recommendation",
  "estimated_effort_hours": 4
}}

IMPORTANT:
- confidence must be between 0.0 and 1.0
- affected_files must be an array (can be empty if not a bug)
- Return ONLY the JSON object, no other text
"""
        return prompt

    async def _execute_claude_code(self, prompt: str, repo_path: str) -> str:
        """
        Execute Claude Code subprocess.

        Args:
            prompt: Analysis prompt
            repo_path: Repository path

        Returns:
            Raw stdout from Claude Code

        Raises:
            subprocess.CalledProcessError: If subprocess fails
        """
        # NOTE: This assumes 'claude' CLI is installed and available
        # Adjust command based on actual Claude Code CLI interface
        cmd = [
            'claude',
            'code',
            '--repo', repo_path,
            '--prompt', prompt,
            '--format', 'json'
        ]

        self.logger.info(f"Executing Claude Code in {repo_path}")

        # Run subprocess
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
            cwd=repo_path
        )

        stdout, stderr = await process.communicate()

        if process.returncode != 0:
            error_msg = stderr.decode('utf-8') if stderr else 'Unknown error'
            raise subprocess.CalledProcessError(
                process.returncode,
                cmd,
                stderr=error_msg
            )

        return stdout.decode('utf-8')

    def _parse_response(self, raw_response: str, method: str) -> CodeAnalysisResult:
        """
        Parse Claude Code JSON response.

        Args:
            raw_response: Raw JSON string
            method: "claude_code" or "llm_fallback"

        Returns:
            CodeAnalysisResult

        Raises:
            ClaudeCodeError: If JSON is invalid or missing fields
        """
        try:
            # Remove markdown code fences if present
            clean_response = raw_response.strip()
            if clean_response.startswith('```'):
                # Extract JSON from markdown code block
                lines = clean_response.split('\n')
                clean_response = '\n'.join(lines[1:-1]) if len(lines) > 2 else clean_response

            data = json.loads(clean_response)

            # Validate required fields
            required_fields = [
                'is_valid_bug', 'confidence', 'root_cause',
                'affected_files', 'fix_recommendation', 'estimated_effort_hours'
            ]
            missing = [f for f in required_fields if f not in data]
            if missing:
                raise ClaudeCodeError(f"Missing required fields: {missing}")

            return CodeAnalysisResult(
                is_valid_bug=data['is_valid_bug'],
                confidence=float(data['confidence']),
                root_cause=data['root_cause'],
                affected_files=data['affected_files'],
                workaround=data.get('workaround'),
                fix_recommendation=data['fix_recommendation'],
                estimated_effort_hours=int(data['estimated_effort_hours']),
                analysis_method=method,
                raw_response=raw_response
            )

        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse JSON response: {e}")
            self.logger.debug(f"Raw response: {raw_response}")
            raise ClaudeCodeError(f"Invalid JSON response: {str(e)}")
        except (KeyError, ValueError, TypeError) as e:
            self.logger.error(f"Invalid response structure: {e}")
            raise ClaudeCodeError(f"Invalid response structure: {str(e)}")

    async def _llm_only_analysis(
        self,
        issue_description: str,
        error_details: Dict[str, Any]
    ) -> CodeAnalysisResult:
        """
        Fallback to LLM-only analysis when Claude Code fails or is disabled.

        This provides a lower-confidence analysis without actual codebase access.

        Args:
            issue_description: Issue summary
            error_details: Technical details

        Returns:
            CodeAnalysisResult with lower confidence
        """
        from app.models.integrations.llm_factory import create_anthropic_llm
        from langchain_core.messages import SystemMessage, HumanMessage

        self.logger.info("Using LLM-only analysis (fallback)")

        try:
            llm = create_anthropic_llm(temperature=0.1, max_tokens=2000)

            system_msg = """You are a technical support analyst. Analyze the issue and provide
            a preliminary assessment. Since you don't have access to the actual codebase,
            your confidence should be lower and recommendations more general."""

            error_code = error_details.get('error_code', 'N/A')
            stack_trace = error_details.get('stack_trace', 'N/A')
            component = error_details.get('component', 'Unknown')

            user_msg = f"""
Analyze this technical issue:

Issue: {issue_description}
Error Code: {error_code}
Component: {component}
Stack Trace: {stack_trace}

Provide analysis in JSON format:
{{
  "is_valid_bug": true/false,
  "confidence": 0.0-1.0 (use lower confidence since no codebase access),
  "root_cause": "Your analysis",
  "affected_files": [],
  "workaround": "Suggested workaround or null",
  "fix_recommendation": "General fix recommendation",
  "estimated_effort_hours": number
}}

Return ONLY the JSON, no markdown or extra text.
"""

            messages = [
                SystemMessage(content=system_msg),
                HumanMessage(content=user_msg)
            ]

            response = llm.invoke(messages)
            return self._parse_response(response.content, "llm_fallback")

        except Exception as e:
            self.logger.error(f"LLM fallback failed: {str(e)}", exc_info=True)

            # Return minimal safe result
            return CodeAnalysisResult(
                is_valid_bug=False,
                confidence=0.3,
                root_cause="Unable to analyze - both Claude Code and LLM fallback failed",
                affected_files=[],
                workaround=None,
                fix_recommendation="Manual investigation required",
                estimated_effort_hours=0,
                analysis_method="error",
                error=str(e)
            )
