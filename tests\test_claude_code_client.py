"""
Tests for Claude Code Client.

Tests subprocess wrapper, timeout handling, JSON parsing, and LLM fallback.
"""
import asyncio
import json
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
import subprocess

from app.models.integrations.claude_code_client import (
    <PERSON><PERSON>ode<PERSON><PERSON>,
    <PERSON><PERSON><PERSON>Erro<PERSON>,
    ClaudeCodeTimeoutError,
    CodeAnalysisResult
)


@pytest.fixture
def client():
    """Create Claude Code client with test config."""
    return ClaudeCodeClient(
        repo_path='/test/repo',
        timeout=5,  # Short timeout for tests
        enabled=True
    )


@pytest.fixture
def sample_error_details():
    """Sample error details for testing."""
    return {
        'error_code': 'DATABASE_CONNECTION_TIMEOUT',
        'stack_trace': 'File "app/db.py", line 89\n  conn = pool.get_connection()',
        'component': 'database'
    }


@pytest.fixture
def valid_claude_response():
    """Valid JSON response from <PERSON>."""
    return json.dumps({
        "is_valid_bug": True,
        "confidence": 0.87,
        "root_cause": "Database connection pool exhausted",
        "affected_files": [
            {"file": "app/db/connection.py", "line": 89, "issue": "max_connections too low"}
        ],
        "workaround": "Increase max_connections",
        "fix_recommendation": "Implement dynamic pooling",
        "estimated_effort_hours": 4
    })


class TestClaudeCodeClient:
    """Test Claude Code client functionality."""

    @pytest.mark.asyncio
    async def test_analyze_issue_success(self, client, sample_error_details, valid_claude_response):
        """Test successful Claude Code invocation."""
        with patch.object(client, '_execute_claude_code', new_callable=AsyncMock) as mock_exec:
            mock_exec.return_value = valid_claude_response

            result = await client.analyze_issue(
                issue_description="Database timeout errors",
                error_details=sample_error_details
            )

            assert isinstance(result, CodeAnalysisResult)
            assert result.is_valid_bug is True
            assert result.confidence == 0.87
            assert result.analysis_method == "claude_code"
            assert len(result.affected_files) == 1
            assert result.affected_files[0]['file'] == "app/db/connection.py"
            mock_exec.assert_called_once()

    @pytest.mark.asyncio
    async def test_claude_code_timeout(self, client, sample_error_details):
        """Test timeout handling with LLM fallback."""
        with patch.object(client, '_execute_claude_code', new_callable=AsyncMock) as mock_exec:
            # Simulate timeout
            mock_exec.side_effect = asyncio.TimeoutError()

            with patch.object(client, '_llm_only_analysis', new_callable=AsyncMock) as mock_llm:
                mock_llm.return_value = CodeAnalysisResult(
                    is_valid_bug=False,
                    confidence=0.5,
                    root_cause="Analysis via LLM fallback",
                    affected_files=[],
                    workaround=None,
                    fix_recommendation="Investigate manually",
                    estimated_effort_hours=2,
                    analysis_method="llm_fallback"
                )

                result = await client.analyze_issue(
                    issue_description="Test issue",
                    error_details=sample_error_details
                )

                assert result.analysis_method == "llm_fallback"
                assert result.confidence <= 0.75  # Lower confidence expected
                mock_llm.assert_called_once()

    @pytest.mark.asyncio
    async def test_subprocess_error(self, client, sample_error_details):
        """Test handling of subprocess errors."""
        with patch.object(client, '_execute_claude_code', new_callable=AsyncMock) as mock_exec:
            mock_exec.side_effect = subprocess.CalledProcessError(
                1, ['claude'], stderr=b"Command not found"
            )

            with patch.object(client, '_llm_only_analysis', new_callable=AsyncMock) as mock_llm:
                mock_llm.return_value = CodeAnalysisResult(
                    is_valid_bug=False,
                    confidence=0.4,
                    root_cause="Subprocess error",
                    affected_files=[],
                    workaround=None,
                    fix_recommendation="Check Claude Code installation",
                    estimated_effort_hours=1,
                    analysis_method="llm_fallback"
                )

                result = await client.analyze_issue(
                    issue_description="Test issue",
                    error_details=sample_error_details
                )

                assert result.analysis_method == "llm_fallback"
                mock_llm.assert_called_once()

    @pytest.mark.asyncio
    async def test_invalid_json_response(self, client, sample_error_details):
        """Test handling of malformed JSON output."""
        with patch.object(client, '_execute_claude_code', new_callable=AsyncMock) as mock_exec:
            mock_exec.return_value = "This is not JSON"

            with patch.object(client, '_llm_only_analysis', new_callable=AsyncMock) as mock_llm:
                mock_llm.return_value = CodeAnalysisResult(
                    is_valid_bug=False,
                    confidence=0.3,
                    root_cause="Analysis failed due to JSON parsing error",
                    affected_files=[],
                    workaround=None,
                    fix_recommendation="Manual investigation required",
                    estimated_effort_hours=0,
                    analysis_method="error"
                )

                result = await client.analyze_issue(
                    issue_description="Test issue",
                    error_details=sample_error_details
                )

                # Should fall back to LLM analysis
                assert result.analysis_method == "error"
                mock_llm.assert_called_once()

    @pytest.mark.asyncio
    async def test_missing_required_fields(self, client, sample_error_details):
        """Test handling of JSON missing required fields."""
        incomplete_response = json.dumps({
            "is_valid_bug": True,
            "confidence": 0.8
            # Missing other required fields
        })

        with patch.object(client, '_execute_claude_code', new_callable=AsyncMock) as mock_exec:
            mock_exec.return_value = incomplete_response

            with patch.object(client, '_llm_only_analysis', new_callable=AsyncMock) as mock_llm:
                mock_llm.return_value = CodeAnalysisResult(
                    is_valid_bug=False,
                    confidence=0.3,
                    root_cause="Analysis failed due to missing fields",
                    affected_files=[],
                    workaround=None,
                    fix_recommendation="Manual investigation required",
                    estimated_effort_hours=0,
                    analysis_method="error"
                )

                result = await client.analyze_issue(
                    issue_description="Test issue",
                    error_details=sample_error_details
                )

                # Should fall back to LLM analysis
                assert result.analysis_method == "error"
                mock_llm.assert_called_once()

    @pytest.mark.asyncio
    async def test_disabled_claude_code(self, sample_error_details):
        """Test behavior when Claude Code is disabled."""
        client = ClaudeCodeClient(enabled=False)

        with patch.object(client, '_llm_only_analysis', new_callable=AsyncMock) as mock_llm:
            mock_llm.return_value = CodeAnalysisResult(
                is_valid_bug=False,
                confidence=0.5,
                root_cause="Claude Code disabled",
                affected_files=[],
                workaround=None,
                fix_recommendation="Enable Claude Code",
                estimated_effort_hours=0,
                analysis_method="llm_fallback"
            )

            result = await client.analyze_issue(
                issue_description="Test issue",
                error_details=sample_error_details
            )

            assert result.analysis_method == "llm_fallback"
            mock_llm.assert_called_once()

    def test_build_prompt(self, client, sample_error_details):
        """Test prompt building."""
        prompt = client._build_prompt(
            issue_description="Database errors",
            error_details=sample_error_details,
            repo_path="/test/repo"
        )

        assert "Database errors" in prompt
        assert "DATABASE_CONNECTION_TIMEOUT" in prompt
        assert "/test/repo" in prompt
        assert "JSON" in prompt
        assert "confidence" in prompt

    def test_parse_response_valid(self, client, valid_claude_response):
        """Test parsing of valid JSON response."""
        result = client._parse_response(valid_claude_response, "claude_code")

        assert isinstance(result, CodeAnalysisResult)
        assert result.is_valid_bug is True
        assert result.confidence == 0.87
        assert result.analysis_method == "claude_code"
        assert result.root_cause == "Database connection pool exhausted"

    def test_parse_response_with_markdown(self, client):
        """Test parsing JSON wrapped in markdown code fences."""
        markdown_response = """```json
{
  "is_valid_bug": true,
  "confidence": 0.9,
  "root_cause": "Test",
  "affected_files": [],
  "workaround": null,
  "fix_recommendation": "Fix it",
  "estimated_effort_hours": 2
}
```"""

        result = client._parse_response(markdown_response, "claude_code")
        assert result.is_valid_bug is True
        assert result.confidence == 0.9

    @pytest.mark.asyncio
    async def test_llm_fallback_success(self, client, sample_error_details):
        """Test successful LLM fallback analysis."""
        valid_llm_response = json.dumps({
            "is_valid_bug": True,
            "confidence": 0.6,
            "root_cause": "LLM analysis result",
            "affected_files": [],
            "workaround": "Restart service",
            "fix_recommendation": "Check configuration",
            "estimated_effort_hours": 3
        })

        with patch('app.models.integrations.llm_factory.create_anthropic_llm') as mock_llm_factory:
            mock_llm = MagicMock()
            mock_llm.invoke.return_value = MagicMock(content=valid_llm_response)
            mock_llm_factory.return_value = mock_llm

            result = await client._llm_only_analysis(
                issue_description="Test issue",
                error_details=sample_error_details
            )

            assert isinstance(result, CodeAnalysisResult)
            assert result.analysis_method == "llm_fallback"
            assert result.confidence == 0.6
            assert result.is_valid_bug is True

    @pytest.mark.asyncio
    async def test_llm_fallback_error(self, client, sample_error_details):
        """Test LLM fallback error handling."""
        with patch('app.models.integrations.llm_factory.create_anthropic_llm') as mock_llm_factory:
            mock_llm_factory.side_effect = Exception("LLM failed")

            result = await client._llm_only_analysis(
                issue_description="Test issue",
                error_details=sample_error_details
            )

            # Should return safe default result
            assert isinstance(result, CodeAnalysisResult)
            assert result.analysis_method == "error"
            assert result.is_valid_bug is False
            assert result.confidence == 0.3
            assert result.error is not None

    @pytest.mark.asyncio
    async def test_custom_repo_path(self, client, sample_error_details, valid_claude_response):
        """Test override of default repo path."""
        custom_path = "/custom/repo"

        with patch.object(client, '_execute_claude_code', new_callable=AsyncMock) as mock_exec:
            mock_exec.return_value = valid_claude_response

            await client.analyze_issue(
                issue_description="Test",
                error_details=sample_error_details,
                repo_path=custom_path
            )

            # Verify custom path was used in prompt
            call_args = mock_exec.call_args
            prompt = call_args[0][0]
            assert custom_path in prompt
