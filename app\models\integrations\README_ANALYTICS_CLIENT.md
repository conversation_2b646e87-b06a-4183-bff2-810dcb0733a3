# PostgreSQL Analytics Client

## Overview

The `PostgresAnalyticsClient` provides a robust, production-ready interface for tracking workflow and agent execution metrics in PostgreSQL. It features connection pooling, graceful error handling, and automatic JSON serialization.

## Features

- ✅ **Connection Pooling**: Efficient database connection management
- ✅ **Graceful Degradation**: Analytics failures never crash workflows
- ✅ **Automatic JSON Handling**: Seamless JSONB serialization
- ✅ **Comprehensive Logging**: Detailed operation logging
- ✅ **Type Safety**: Full type hints for IDE support
- ✅ **Test Coverage**: >89% unit test coverage

## Installation

The client is automatically initialized as a singleton:

```python
from app.models.integrations.postgres_analytics_client import postgres_analytics_client
```

## Usage Examples

### 1. Recording Workflow Execution

```python
from datetime import datetime
from app.models.integrations.postgres_analytics_client import postgres_analytics_client

# At workflow start
correlation_id = f"{ticket_id}-{int(datetime.now().timestamp())}"

success = postgres_analytics_client.insert_workflow_execution(
    correlation_id=correlation_id,
    ticket_id="AIA-123",
    workflow_start_time=datetime.now(),
    workflow_status="running",
    stages_total=4,
    webhook_payload={"issue": {"key": "AIA-123", "summary": "Test issue"}}
)

if not success:
    logger.warning("Failed to log workflow start - continuing anyway")
```

### 2. Updating Workflow on Completion

```python
# At workflow completion
success = postgres_analytics_client.update_workflow_execution(
    correlation_id=correlation_id,
    workflow_end_time=datetime.now(),
    total_execution_time_ms=5000,
    workflow_status="completed",
    stages_completed=4,
    agents_executed=10,
    agents_completed=9,
    agents_failed=0,
    agents_skipped=1
)
```

### 3. Recording Agent Execution

```python
# After agent completes
success = postgres_analytics_client.insert_agent_execution(
    correlation_id=correlation_id,
    ticket_id="AIA-123",
    agent_name="sentiment",
    stage_name="stage1",
    start_time=start_time,
    end_time=datetime.now(),
    execution_time_ms=150,
    agent_status="completed",
    should_execute_result=True,
    output_data={
        "sentiment_score": 0.8,
        "confidence": 0.95,
        "classification": "positive"
    }
)
```

### 4. Recording Failed Agent

```python
success = postgres_analytics_client.insert_agent_execution(
    correlation_id=correlation_id,
    ticket_id="AIA-123",
    agent_name="escalation",
    stage_name="stage1",
    start_time=start_time,
    agent_status="failed",
    error_message="API timeout after 30s",
    execution_time_ms=30000
)
```

### 5. Recording Skipped Agent

```python
success = postgres_analytics_client.insert_agent_execution(
    correlation_id=correlation_id,
    ticket_id="AIA-123",
    agent_name="auto_response",
    stage_name="stage2",
    start_time=datetime.now(),
    agent_status="skipped",
    should_execute_result=False,
    execution_time_ms=0
)
```

### 6. Querying Workflow Statistics

```python
# Query by correlation_id (single result)
workflow = postgres_analytics_client.get_workflow_stats(
    correlation_id="AIA-123-1696800000"
)

if workflow:
    print(f"Status: {workflow['workflow_status']}")
    print(f"Total time: {workflow['total_execution_time_ms']}ms")
    print(f"Agents completed: {workflow['agents_completed']}")

# Query by ticket_id (list of results)
workflows = postgres_analytics_client.get_workflow_stats(
    ticket_id="AIA-123"
)

for wf in workflows:
    print(f"Correlation ID: {wf['correlation_id']}")
    print(f"Status: {wf['workflow_status']}")
```

## Integration with WorkflowOrchestrator

### Example Integration

```python
class WorkflowOrchestrator:

    async def execute_workflow(self, ticket_data: Dict[str, Any]) -> WorkflowContext:
        """Execute workflow with analytics tracking."""
        context = WorkflowContext(
            ticket_id=ticket_data['ticket_id'],
            correlation_id=f"{ticket_data['ticket_id']}-{int(datetime.now().timestamp())}",
            ticket_data=ticket_data,
            stage_results={},
            metadata={"start_time": datetime.now().isoformat()}
        )

        # 1. Log workflow start
        try:
            postgres_analytics_client.insert_workflow_execution(
                correlation_id=context.correlation_id,
                ticket_id=context.ticket_id,
                workflow_start_time=datetime.now(),
                workflow_status='running',
                webhook_payload=ticket_data
            )
        except Exception as e:
            logger.error(f"Analytics logging failed: {e}")
            # Continue anyway - never let analytics break workflows

        # 2. Execute stages
        for stage_name, agents in self.stages.items():
            results = await self.execute_stage(stage_name, agents, context)

        # 3. Update workflow completion
        try:
            end_time = datetime.now()
            start_time = datetime.fromisoformat(context.metadata['start_time'])
            execution_time = int((end_time - start_time).total_seconds() * 1000)

            postgres_analytics_client.update_workflow_execution(
                correlation_id=context.correlation_id,
                workflow_end_time=end_time,
                total_execution_time_ms=execution_time,
                workflow_status='completed',
                stages_completed=4,
                agents_executed=count_total,
                agents_completed=count_completed,
                agents_failed=count_failed,
                agents_skipped=count_skipped
            )
        except Exception as e:
            logger.error(f"Analytics update failed: {e}")

        return context
```

### Example BaseAgent Integration

```python
class BaseAgent:

    async def execute(self, context: WorkflowContext) -> AgentResult:
        """Execute agent with analytics tracking."""
        start_time = datetime.now()

        try:
            # Check if agent should run
            should_run = await self.should_execute(context)

            if not should_run:
                # Log skipped agent
                postgres_analytics_client.insert_agent_execution(
                    correlation_id=context.correlation_id,
                    ticket_id=context.ticket_id,
                    agent_name=self.name,
                    stage_name=self.stage,
                    start_time=start_time,
                    agent_status='skipped',
                    should_execute_result=False,
                    execution_time_ms=0
                )

                return AgentResult(
                    agent_name=self.name,
                    status=AgentStatus.SKIPPED,
                    output={},
                    execution_time=0.0
                )

            # Execute agent
            output = await self.process(context)
            end_time = datetime.now()
            execution_ms = int((end_time - start_time).total_seconds() * 1000)

            # Log successful execution
            postgres_analytics_client.insert_agent_execution(
                correlation_id=context.correlation_id,
                ticket_id=context.ticket_id,
                agent_name=self.name,
                stage_name=self.stage,
                start_time=start_time,
                end_time=end_time,
                execution_time_ms=execution_ms,
                agent_status='completed',
                should_execute_result=True,
                output_data=output
            )

            return AgentResult(
                agent_name=self.name,
                status=AgentStatus.COMPLETED,
                output=output,
                execution_time=execution_ms / 1000.0
            )

        except Exception as e:
            end_time = datetime.now()
            execution_ms = int((end_time - start_time).total_seconds() * 1000)

            # Log failed execution
            postgres_analytics_client.insert_agent_execution(
                correlation_id=context.correlation_id,
                ticket_id=context.ticket_id,
                agent_name=self.name,
                stage_name=self.stage,
                start_time=start_time,
                agent_status='failed',
                error_message=str(e),
                execution_time_ms=execution_ms
            )

            raise
```

## Error Handling

The client implements graceful degradation:

```python
# If database is unavailable, operations return False but don't crash
success = postgres_analytics_client.insert_workflow_execution(...)
if not success:
    logger.warning("Analytics logging failed - continuing workflow")
    # Workflow continues normally
```

## Configuration

Set these environment variables:

```bash
POSTGRESQL_HOST=localhost
POSTGRESQL_PORT=5432
POSTGRESQL_USER=postgres
POSTGRESQL_PASSWORD=secure_password
POSTGRESQL_DATABASE=ai_agents_analytics
```

## Connection Pool Configuration

Default pool settings:
- Minimum connections: 1
- Maximum connections: 10

Custom pool size:

```python
client = PostgresAnalyticsClient(min_conn=5, max_conn=20)
```

## Closing Connections

```python
# On application shutdown
postgres_analytics_client.close()
```

## API Reference

### insert_workflow_execution()

Insert new workflow execution record at start.

**Parameters:**
- `correlation_id` (str): Unique identifier for distributed tracing
- `ticket_id` (str): Jira ticket ID
- `workflow_start_time` (datetime): Workflow start timestamp
- `workflow_status` (str): Status ('running', 'completed', 'failed', 'partial')
- `stages_total` (int, optional): Total stages (default: 4)
- `webhook_payload` (dict, optional): Original webhook JSON

**Returns:** `bool` - True if successful

### update_workflow_execution()

Update workflow with final metrics.

**Parameters:**
- `correlation_id` (str): Workflow correlation ID
- `workflow_end_time` (datetime, optional): Completion timestamp
- `total_execution_time_ms` (int, optional): Total execution time in ms
- `workflow_status` (str, optional): Final status
- `error_message` (str, optional): Error message if failed
- `stages_completed` (int, optional): Completed stages count
- `agents_executed` (int, optional): Total agents executed
- `agents_completed` (int, optional): Completed agents count
- `agents_failed` (int, optional): Failed agents count
- `agents_skipped` (int, optional): Skipped agents count

**Returns:** `bool` - True if successful

### insert_agent_execution()

Insert agent execution record.

**Parameters:**
- `correlation_id` (str): Workflow correlation ID
- `ticket_id` (str): Jira ticket ID
- `agent_name` (str): Agent name
- `stage_name` (str): Stage identifier
- `start_time` (datetime): Start timestamp
- `agent_status` (str): Status ('completed', 'failed', 'skipped')
- `end_time` (datetime, optional): End timestamp
- `execution_time_ms` (int, optional): Execution time in ms
- `execution_order` (int, optional): Order within workflow
- `should_execute_result` (bool, optional): Conditional execution flag
- `error_message` (str, optional): Error message if failed
- `output_data` (dict, optional): Agent output (stored as JSONB)
- `output_summary` (str, optional): Human-readable summary

**Returns:** `bool` - True if successful

### get_workflow_stats()

Query workflow statistics.

**Parameters:**
- `correlation_id` (str, optional): Query by correlation ID (returns single dict)
- `ticket_id` (str, optional): Query by ticket ID (returns list of dicts)

**Returns:** `dict | list[dict] | None` - Workflow data or None if not found

## Testing

Run unit tests:
```bash
pytest tests/test_postgres_analytics_client.py -v --cov
```

Run integration tests:
```bash
pytest tests/test_postgres_analytics_integration.py -v
```

## Performance Considerations

- Connection pooling minimizes connection overhead
- JSONB fields allow flexible schema without migrations
- Indexes optimize common query patterns
- Async-safe (can be called from async contexts)

## Troubleshooting

**Connection errors:**
- Verify PostgreSQL is running
- Check environment variables
- Confirm database exists
- Validate user permissions

**Insert failures:**
- Check foreign key constraints (agents need parent workflows)
- Verify unique correlation_id constraint
- Review PostgreSQL logs

**Performance issues:**
- Increase connection pool size
- Review query execution plans
- Check database indexes
- Monitor connection pool utilization

## Related Documentation

- [Database Migration](../../../db/migrations/001_create_analytics_tables.sql)
- [Confluence Architecture Doc](https://sanasai.atlassian.net/wiki/spaces/AF/pages/566198356)
- [Jira Ticket AIA-91](https://sanasai.atlassian.net/browse/AIA-91)
