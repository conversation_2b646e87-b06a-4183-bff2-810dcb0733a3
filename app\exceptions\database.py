"""
Database-related custom exceptions.

These exceptions provide structured error handling for database operations
with graceful degradation support.
"""


class DatabaseException(Exception):
    """Base exception for all database-related errors."""
    pass


class ConnectionPoolInitError(DatabaseException):
    """Raised when connection pool fails to initialize."""

    def __init__(self, original_error: Exception = None):
        self.original_error = original_error
        message = "Failed to initialize database connection pool"
        if original_error:
            message += f": {str(original_error)}"
        super().__init__(message)


class ConnectionPoolError(DatabaseException):
    """Raised when unable to get connection from pool."""

    def __init__(self, operation: str = None):
        message = "No database connection available"
        if operation:
            message += f" for operation: {operation}"
        super().__init__(message)


class QueryExecutionError(DatabaseException):
    """Raised when database query execution fails."""

    def __init__(self, operation: str, original_error: Exception = None):
        self.operation = operation
        self.original_error = original_error
        message = f"Failed to execute database operation: {operation}"
        if original_error:
            message += f" - {str(original_error)}"
        super().__init__(message)
