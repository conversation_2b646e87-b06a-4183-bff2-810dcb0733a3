"""Integration tests for JiraClient using real JIRA API."""
import pytest
import os
from dotenv import load_dotenv
from app.models.integrations.jira_client import JiraClient

# Load environment variables
load_dotenv()

# Test configuration from .env
TEST_PROJECT_KEY = os.getenv('TEST_JIRA_PROJECT', 'AIA')
JIRA_URL = os.getenv('JIRA_URL')
JIRA_EMAIL = os.getenv('JIRA_EMAIL')
JIRA_API_TOKEN = os.getenv('JIRA_API_TOKEN')


@pytest.fixture(scope='module')
def jira_client():
    """Create a real JiraClient instance."""
    if not all([JIRA_URL, JIRA_EMAIL, JIRA_API_TOKEN]):
        pytest.skip("JIRA credentials not configured in .env file")

    client = JiraClient()
    if client.client is None:
        pytest.skip("Failed to initialize JIRA client")

    return client


@pytest.fixture(scope='module')
def test_issue_key(jira_client):
    """
    Get a test issue key from the test project.
    This will fetch the first available issue from the project.
    """
    issues = jira_client.get_issues_by_project(TEST_PROJECT_KEY, max_results=1)
    if not issues:
        pytest.skip(f"No issues found in project {TEST_PROJECT_KEY}")

    return issues[0]['key']


class TestJiraConnection:
    """Test JIRA connection and authentication."""

    def test_client_initialization(self, jira_client):
        """Test that JIRA client initializes successfully."""
        assert jira_client is not None
        assert jira_client.client is not None

    def test_authentication(self, jira_client):
        """Test that authentication works by fetching project details."""
        project = jira_client.get_project(TEST_PROJECT_KEY)
        assert project is not None
        assert project['key'] == TEST_PROJECT_KEY
        assert 'name' in project


class TestGetProject:
    """Tests for get_project method."""

    def test_get_project_success(self, jira_client):
        """Test successfully retrieving project details."""
        project = jira_client.get_project(TEST_PROJECT_KEY)

        assert project is not None
        assert project['key'] == TEST_PROJECT_KEY
        assert 'name' in project
        assert 'lead' in project
        print(f"\nProject: {project['name']} (Lead: {project['lead']})")

    def test_get_project_not_found(self, jira_client):
        """Test getting a non-existent project."""
        project = jira_client.get_project('INVALIDPROJECT999')
        assert project is None


class TestGetIssuesByProject:
    """Tests for get_issues_by_project method."""

    def test_get_issues_success(self, jira_client):
        """Test successfully retrieving issues from project."""
        issues = jira_client.get_issues_by_project(TEST_PROJECT_KEY, max_results=10)

        assert isinstance(issues, list)
        if len(issues) > 0:
            first_issue = issues[0]
            assert 'key' in first_issue
            assert 'summary' in first_issue
            assert 'status' in first_issue
            assert first_issue['key'].startswith(TEST_PROJECT_KEY)
            print(f"\nFound {len(issues)} issues in project {TEST_PROJECT_KEY}")
            print(f"First issue: {first_issue['key']} - {first_issue['summary']}")

    def test_get_issues_with_pagination(self, jira_client):
        """Test pagination parameters."""
        # Get first 5 issues
        first_batch = jira_client.get_issues_by_project(TEST_PROJECT_KEY, max_results=5, start_at=0)

        # Get next 5 issues
        second_batch = jira_client.get_issues_by_project(TEST_PROJECT_KEY, max_results=5, start_at=5)

        assert isinstance(first_batch, list)
        assert isinstance(second_batch, list)

        # If both batches have results, verify they're different
        if first_batch and second_batch:
            first_keys = {issue['key'] for issue in first_batch}
            second_keys = {issue['key'] for issue in second_batch}
            # Check that there's no overlap (different pages)
            assert len(first_keys.intersection(second_keys)) == 0

    def test_get_issues_empty_project(self, jira_client):
        """Test getting issues from non-existent project."""
        issues = jira_client.get_issues_by_project('NONEXISTENT999')
        assert issues == []


class TestGetIssue:
    """Tests for get_issue method."""

    def test_get_issue_success(self, jira_client, test_issue_key):
        """Test successfully retrieving a single issue."""
        issue = jira_client.get_issue(test_issue_key)

        assert issue is not None
        assert issue['key'] == test_issue_key
        assert 'summary' in issue
        assert 'description' in issue
        assert 'status' in issue
        assert 'priority' in issue
        assert 'created' in issue
        assert 'updated' in issue
        assert 'issue_type' in issue

        print(f"\nIssue: {issue['key']}")
        print(f"Summary: {issue['summary']}")
        print(f"Status: {issue['status']}")
        print(f"Priority: {issue['priority']}")

    def test_get_issue_not_found(self, jira_client):
        """Test getting a non-existent issue."""
        issue = jira_client.get_issue(f'{TEST_PROJECT_KEY}-999999')
        assert issue is None


class TestSearchIssues:
    """Tests for search_issues method."""

    def test_search_issues_by_project(self, jira_client):
        """Test searching issues with JQL query."""
        jql = f'project={TEST_PROJECT_KEY}'
        issues = jira_client.search_issues(jql, max_results=10)

        assert isinstance(issues, list)
        if issues:
            for issue in issues:
                assert issue['key'].startswith(TEST_PROJECT_KEY)
            print(f"\nSearch found {len(issues)} issues for JQL: {jql}")

    def test_search_issues_with_status(self, jira_client):
        """Test searching issues with status filter."""
        jql = f'project={TEST_PROJECT_KEY} AND status="Done"'
        issues = jira_client.search_issues(jql, max_results=5)

        assert isinstance(issues, list)
        if issues:
            for issue in issues:
                # Note: Status might vary, just checking structure
                assert 'status' in issue
            print(f"\nSearch with status filter found {len(issues)} issues")

    def test_search_issues_empty_result(self, jira_client):
        """Test search with query that returns no results."""
        jql = f'project={TEST_PROJECT_KEY} AND summary~"NONEXISTENT_TICKET_XYZ123"'
        issues = jira_client.search_issues(jql)

        assert isinstance(issues, list)
        assert len(issues) == 0


class TestGetIssueComments:
    """Tests for get_issue_comments method."""

    def test_get_issue_comments(self, jira_client, test_issue_key):
        """Test retrieving comments from an issue."""
        comments = jira_client.get_issue_comments(test_issue_key)

        assert isinstance(comments, list)

        if comments:
            first_comment = comments[0]
            assert 'id' in first_comment
            assert 'author' in first_comment
            assert 'body' in first_comment
            assert 'created' in first_comment
            print(f"\nFound {len(comments)} comments on {test_issue_key}")
            print(f"First comment by: {first_comment['author']}")
        else:
            print(f"\nNo comments found on {test_issue_key}")

    def test_get_comments_invalid_issue(self, jira_client):
        """Test getting comments from non-existent issue."""
        comments = jira_client.get_issue_comments(f'{TEST_PROJECT_KEY}-999999')
        assert comments == []


class TestPostComment:
    """Tests for post_comment method."""

    def test_post_comment_success(self, jira_client, test_issue_key):
        """Test posting a comment to an issue."""
        comment_text = "Test comment from automated integration tests"

        result = jira_client.post_comment(test_issue_key, comment_text)

        assert result is True

        # Verify the comment was added
        comments = jira_client.get_issue_comments(test_issue_key)
        assert any(comment_text in comment['body'] for comment in comments)
        print(f"\nSuccessfully added comment to {test_issue_key}")

    def test_post_comment_invalid_issue(self, jira_client):
        """Test posting comment to non-existent issue."""
        result = jira_client.post_comment(f'{TEST_PROJECT_KEY}-999999', 'Test comment')
        assert result is False


class TestGetIssueAttachments:
    """Tests for get_issue_attachments method."""

    def test_get_issue_attachments(self, jira_client, test_issue_key):
        """Test retrieving attachments from an issue."""
        attachments = jira_client.get_issue_attachments(test_issue_key)

        assert isinstance(attachments, list)

        if attachments:
            first_attachment = attachments[0]
            assert 'id' in first_attachment
            assert 'filename' in first_attachment
            assert 'size' in first_attachment
            assert 'content_url' in first_attachment
            print(f"\nFound {len(attachments)} attachments on {test_issue_key}")
            print(f"First attachment: {first_attachment['filename']}")
        else:
            print(f"\nNo attachments found on {test_issue_key}")

    def test_get_attachments_invalid_issue(self, jira_client):
        """Test getting attachments from non-existent issue."""
        attachments = jira_client.get_issue_attachments(f'{TEST_PROJECT_KEY}-999999')
        assert attachments == []


class TestGetTransitions:
    """Tests for get_transitions method."""

    def test_get_transitions(self, jira_client, test_issue_key):
        """Test getting available transitions for an issue."""
        transitions = jira_client.get_transitions(test_issue_key)

        assert isinstance(transitions, list)

        if transitions:
            first_transition = transitions[0]
            assert 'id' in first_transition
            assert 'name' in first_transition
            print(f"\nAvailable transitions for {test_issue_key}:")
            for transition in transitions:
                print(f"  - {transition['name']} (ID: {transition['id']})")
        else:
            print(f"\nNo transitions available for {test_issue_key}")

    def test_get_transitions_invalid_issue(self, jira_client):
        """Test getting transitions from non-existent issue."""
        transitions = jira_client.get_transitions(f'{TEST_PROJECT_KEY}-999999')
        assert transitions == []


class TestGetIssueTypes:
    """Tests for get_issue_types method."""

    def test_get_issue_types(self, jira_client):
        """Test getting issue types for a project."""
        issue_types = jira_client.get_issue_types(TEST_PROJECT_KEY)

        assert isinstance(issue_types, list)
        assert len(issue_types) > 0

        first_type = issue_types[0]
        assert 'id' in first_type
        assert 'name' in first_type

        print(f"\nIssue types for {TEST_PROJECT_KEY}:")
        for issue_type in issue_types:
            print(f"  - {issue_type['name']} (ID: {issue_type['id']})")

    def test_get_issue_types_invalid_project(self, jira_client):
        """Test getting issue types for non-existent project."""
        issue_types = jira_client.get_issue_types('INVALIDPROJECT999')
        assert issue_types == []


class TestGetPriorities:
    """Tests for get_priorities method."""

    def test_get_priorities(self, jira_client):
        """Test getting all priorities."""
        priorities = jira_client.get_priorities()

        assert isinstance(priorities, list)
        assert len(priorities) > 0

        first_priority = priorities[0]
        assert 'id' in first_priority
        assert 'name' in first_priority

        print(f"\nAvailable priorities:")
        for priority in priorities:
            print(f"  - {priority['name']} (ID: {priority['id']})")


class TestUpdateOperations:
    """Tests for update operations (labels, priority, fields)."""

    def test_add_labels(self, jira_client, test_issue_key):
        """Test adding labels to an issue."""
        test_label = 'automated-test'

        result = jira_client.add_labels(test_issue_key, [test_label])

        assert result is True

        # Verify the label was added
        issue = jira_client.get_issue(test_issue_key)
        assert test_label in issue['labels']
        print(f"\nSuccessfully added label '{test_label}' to {test_issue_key}")

    def test_update_issue_fields(self, jira_client, test_issue_key):
        """Test updating issue fields."""
        # Update description (safe field to update)
        new_description = "Updated by automated integration test"

        result = jira_client.update_issue_fields(
            test_issue_key,
            {'description': new_description}
        )

        assert result is True

        # Verify the update
        issue = jira_client.get_issue(test_issue_key)
        assert new_description in issue['description']
        print(f"\nSuccessfully updated description for {test_issue_key}")


class TestAssignIssue:
    """Tests for assign_issue method."""

    def test_assign_issue_to_current_user(self, jira_client, test_issue_key):
        """Test assigning issue to current user (yourself)."""
        # Assign to current user using their email
        result = jira_client.assign_issue(test_issue_key, JIRA_EMAIL)

        # This might fail if user doesn't have permission, which is acceptable
        if result:
            print(f"\nSuccessfully assigned {test_issue_key} to {JIRA_EMAIL}")
        else:
            print(f"\nCould not assign {test_issue_key} (permission may be required)")


# Summary test to verify all core functionality
class TestFullWorkflow:
    """Test a complete workflow using real JIRA data."""

    def test_complete_workflow(self, jira_client):
        """Test a complete workflow: search, get, comment."""
        # 1. Get project details
        project = jira_client.get_project(TEST_PROJECT_KEY)
        assert project is not None
        print(f"\n=== Complete Workflow Test ===")
        print(f"1. Project: {project['name']}")

        # 2. Get issues from project
        issues = jira_client.get_issues_by_project(TEST_PROJECT_KEY, max_results=5)
        assert len(issues) > 0
        print(f"2. Found {len(issues)} issues")

        # 3. Get detailed info about first issue
        issue_key = issues[0]['key']
        issue = jira_client.get_issue(issue_key)
        assert issue is not None
        print(f"3. Issue details: {issue['key']} - {issue['summary']}")

        # 4. Get comments
        comments = jira_client.get_issue_comments(issue_key)
        print(f"4. Issue has {len(comments)} comments")

        # 5. Add a test comment
        result = jira_client.post_comment(issue_key, "Workflow test comment")
        assert result is True
        print(f"5. Successfully added comment")

        print("=== Workflow Complete ===")
