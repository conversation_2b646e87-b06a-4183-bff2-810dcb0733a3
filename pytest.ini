[pytest]
# Test discovery
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Output options
addopts =
    -v
    --tb=short
    --strict-markers
    --disable-warnings

# Markers
markers =
    asyncio: mark test as an async test
    integration: mark test as an integration test
    unit: mark test as a unit test

# pytest-asyncio mode
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function
