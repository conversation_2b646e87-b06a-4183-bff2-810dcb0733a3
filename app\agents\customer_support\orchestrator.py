import asyncio
import logging
from datetime import datetime
from typing import Dict, Any, List

# Import agents module to trigger registrations
import app.agents.customer_support

from app.agents.customer_support.base_agent import BaseAgent, WorkflowContext, AgentResult, AgentStatus
from app.agents.registry import get_agent_class
from config.agent_stages import STAGES, get_all_stages

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class WorkflowOrchestrator:
    """
    Main orchestrator for multi-stage agent workflow.

    Uses registry pattern to dynamically load agents based on configuration.
    No hardcoded agent imports - agents self-register via decorators.
    """

    def __init__(self):
        """Initialize orchestrator with stage configuration."""
        self.stages = self._initialize_stages()
        logger.info(f"Orchestrator initialized with {len(self.stages)} stages")

    def _initialize_stages(self) -> Dict[str, List[BaseAgent]]:
        """
        Initialize stages by loading agents from registry based on configuration.

        Returns:
            Dictionary mapping stage names to lists of agent instances
        """
        stages = {}

        for stage_name in get_all_stages():
            agent_names = STAGES.get(stage_name, [])
            agents = []

            for agent_name in agent_names:
                # Look up agent class from registry
                agent_class = get_agent_class(stage_name, agent_name)

                if agent_class is None:
                    logger.warning(
                        f"Agent '{agent_name}' not found in registry for {stage_name}. "
                        f"Make sure it's decorated with @register_agent(stage='{stage_name}', name='{agent_name}')"
                    )
                    continue

                # Instantiate agent
                try:
                    agent_instance = agent_class(agent_name)
                    agents.append(agent_instance)
                    logger.debug(f"Loaded agent: {stage_name}.{agent_name}")
                except Exception as e:
                    logger.error(f"Failed to instantiate {agent_name}: {str(e)}")

            stages[stage_name] = agents
            logger.info(f"{stage_name}: loaded {len(agents)} agents")

        return stages

    async def execute_stage(
            self,
            stage_name: str,
            agents: List[BaseAgent],
            context: WorkflowContext
    ) -> List[AgentResult]:
        """
        Execute all agents in a stage in parallel.

        Agents are checked for conditional execution via should_execute() method.
        If should_execute() returns False, the agent is skipped.
        """
        logger.info(f"[{context.correlation_id}] Executing {stage_name}")

        tasks = []
        for agent in agents:
            # Check conditional execution (all agents now have should_execute method)
            should_run = await agent.should_execute(context)
            if not should_run:
                logger.info(f"[{context.correlation_id}] Skipping {agent.name}")
                tasks.append(self._create_skipped_result(agent))
                continue

            tasks.append(agent.execute(context))

        results = await asyncio.gather(*tasks)
        context.stage_results[stage_name] = results

        return results

    def _create_skipped_result(self, agent: BaseAgent) -> AgentResult:
        """Create a result for skipped agents"""

        return AgentResult(
            agent_name=agent.name,
            status=AgentStatus.SKIPPED,
            output={},
            execution_time=0.0
        )

    async def execute_workflow(self, ticket_data: Dict[str, Any]) -> WorkflowContext:
        """Execute the entire workflow"""
        context = WorkflowContext(
            ticket_id=ticket_data.get("ticket_id", "UNKNOWN"),
            correlation_id=ticket_data.get("correlation_id", "UNKNOWN"),
            ticket_data=ticket_data,
            stage_results={},
            metadata={"start_time": datetime.now().isoformat()}
        )

        logger.info(f"[{context.correlation_id}] Starting workflow for ticket {context.ticket_id}")

        try:
            # Execute stages sequentially, agents within stages run in parallel
            for stage_name, agents in self.stages.items():
                results = await self.execute_stage(stage_name, agents, context)

                # Log stage completion
                completed = sum(1 for r in results if r.status == AgentStatus.COMPLETED)
                failed = sum(1 for r in results if r.status == AgentStatus.FAILED)
                skipped = sum(1 for r in results if r.status == AgentStatus.SKIPPED)

                logger.info(
                    f"[{context.correlation_id}] {stage_name} complete: "
                    f"{completed} succeeded, {failed} failed, {skipped} skipped"
                )

                # Early termination on critical failures (optional)
                if stage_name == "stage1" and failed > 0:
                    logger.warning(f"[{context.correlation_id}] Stage 1 had failures, continuing anyway")

            context.metadata["end_time"] = datetime.now().isoformat()
            logger.info(f"[{context.correlation_id}] Workflow completed successfully")

        except Exception as e:
            logger.error(f"[{context.correlation_id}] Workflow failed: {str(e)}")
            context.metadata["error"] = str(e)

        return context

    def get_workflow_summary(self, context: WorkflowContext) -> Dict[str, Any]:
        """Generate workflow execution summary"""
        summary = {
            "ticket_id": context.ticket_id,
            "correlation_id": context.correlation_id,
            "stages": {}
        }

        for stage_name, results in context.stage_results.items():
            summary["stages"][stage_name] = {
                "total_agents": len(results),
                "completed": sum(1 for r in results if r.status == AgentStatus.COMPLETED),
                "failed": sum(1 for r in results if r.status == AgentStatus.FAILED),
                "skipped": sum(1 for r in results if r.status == AgentStatus.SKIPPED),
                "total_time": sum(r.execution_time for r in results),
                "agents": [
                    {
                        "name": r.agent_name,
                        "status": r.status.value,
                        "execution_time": r.execution_time,
                        "output_keys": list(r.output.keys()) if r.output else []
                    }
                    for r in results
                ]
            }

        return summary
